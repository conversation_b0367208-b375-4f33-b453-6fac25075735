using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using HorizonLoader.Shared.Models;

namespace HorizonLoader.Server.Services
{
    /// <summary>
    /// Provides serial key generation and management functionality
    /// </summary>
    public class SerialKeyService
    {
        private readonly DatabaseService _databaseService;
        private readonly Random _random;

        public SerialKeyService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _random = new Random();
        }

        /// <summary>
        /// Generates a new serial key with specified parameters
        /// </summary>
        public SerialKey GenerateSerialKey(string productId, string productName, DateTime expirationDate, string createdBy = "Admin", int maxHwidResets = 3)
        {
            if (string.IsNullOrEmpty(productId))
                throw new ArgumentException("Product ID cannot be null or empty", nameof(productId));

            if (string.IsNullOrEmpty(productName))
                throw new ArgumentException("Product name cannot be null or empty", nameof(productName));

            if (expirationDate <= DateTime.UtcNow)
                throw new ArgumentException("Expiration date must be in the future", nameof(expirationDate));

            string key = GenerateUniqueKey();
            
            var serialKey = new SerialKey
            {
                Key = key,
                ProductId = productId,
                ProductName = productName,
                ExpirationDate = expirationDate,
                CreatedBy = createdBy,
                MaxHwidResets = maxHwidResets
            };

            if (_databaseService.AddSerialKey(serialKey))
            {
                return serialKey;
            }

            throw new InvalidOperationException("Failed to add serial key to database");
        }

        /// <summary>
        /// Generates a unique serial key string
        /// </summary>
        private string GenerateUniqueKey()
        {
            string key;
            int attempts = 0;
            const int maxAttempts = 100;

            do
            {
                key = GenerateKeyString();
                attempts++;
                
                if (attempts >= maxAttempts)
                    throw new InvalidOperationException("Failed to generate unique serial key after maximum attempts");
                    
            } while (_databaseService.GetSerialKey(key) != null);

            return key;
        }

        /// <summary>
        /// Generates a formatted serial key string
        /// </summary>
        private string GenerateKeyString()
        {
            // Generate a key in format: XXXX-XXXX-XXXX-XXXX
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var segments = new string[4];

            for (int segment = 0; segment < 4; segment++)
            {
                var segmentChars = new char[4];
                for (int i = 0; i < 4; i++)
                {
                    segmentChars[i] = chars[_random.Next(chars.Length)];
                }
                segments[segment] = new string(segmentChars);
            }

            return string.Join("-", segments);
        }

        /// <summary>
        /// Validates a serial key and returns validation result
        /// </summary>
        public SerialKeyValidationResult ValidateSerialKey(string key, string hwid)
        {
            if (string.IsNullOrEmpty(key))
                return new SerialKeyValidationResult { IsValid = false, ErrorMessage = "Serial key is required" };

            if (string.IsNullOrEmpty(hwid))
                return new SerialKeyValidationResult { IsValid = false, ErrorMessage = "Hardware ID is required" };

            var serialKey = _databaseService.GetSerialKey(key);
            if (serialKey == null)
                return new SerialKeyValidationResult { IsValid = false, ErrorMessage = "Invalid serial key" };

            if (!serialKey.IsActive)
                return new SerialKeyValidationResult { IsValid = false, ErrorMessage = "Serial key is deactivated" };

            if (serialKey.IsExpired())
                return new SerialKeyValidationResult { IsValid = false, ErrorMessage = "Serial key has expired" };

            // Check HWID binding
            if (!string.IsNullOrEmpty(serialKey.BoundHwid))
            {
                if (!serialKey.IsBoundToHwid(hwid))
                {
                    return new SerialKeyValidationResult 
                    { 
                        IsValid = false, 
                        ErrorMessage = "Hardware ID mismatch",
                        RequiresHwidReset = true,
                        CanResetHwid = serialKey.CanPerformHwidReset(),
                        RemainingHwidResets = serialKey.MaxHwidResets - serialKey.HwidResetCount
                    };
                }
            }
            else
            {
                // Bind to current HWID on first use
                serialKey.BindToHwid(hwid);
                _databaseService.UpdateSerialKey(serialKey);
            }

            return new SerialKeyValidationResult 
            { 
                IsValid = true, 
                SerialKey = serialKey,
                ExpirationDate = serialKey.ExpirationDate,
                ProductAccess = new[] { serialKey.ProductId }.ToList()
            };
        }

        /// <summary>
        /// Resets the HWID binding for a serial key
        /// </summary>
        public HwidResetResult ResetHwid(string key, string adminPassword)
        {
            if (string.IsNullOrEmpty(key))
                return new HwidResetResult { Success = false, ErrorMessage = "Serial key is required" };

            // Validate admin password (in production, this should be properly hashed and verified)
            var config = _databaseService.GetConfiguration();
            if (adminPassword != config.AdminPassword)
                return new HwidResetResult { Success = false, ErrorMessage = "Invalid admin password" };

            var serialKey = _databaseService.GetSerialKey(key);
            if (serialKey == null)
                return new HwidResetResult { Success = false, ErrorMessage = "Serial key not found" };

            if (!serialKey.CanPerformHwidReset())
                return new HwidResetResult { Success = false, ErrorMessage = "HWID reset limit reached or not allowed" };

            if (serialKey.ResetHwid())
            {
                _databaseService.UpdateSerialKey(serialKey);
                return new HwidResetResult 
                { 
                    Success = true, 
                    Message = "HWID reset successfully",
                    RemainingResets = serialKey.MaxHwidResets - serialKey.HwidResetCount
                };
            }

            return new HwidResetResult { Success = false, ErrorMessage = "Failed to reset HWID" };
        }

        /// <summary>
        /// Deactivates a serial key
        /// </summary>
        public bool DeactivateSerialKey(string key)
        {
            var serialKey = _databaseService.GetSerialKey(key);
            if (serialKey == null)
                return false;

            serialKey.IsActive = false;
            return _databaseService.UpdateSerialKey(serialKey);
        }

        /// <summary>
        /// Activates a serial key
        /// </summary>
        public bool ActivateSerialKey(string key)
        {
            var serialKey = _databaseService.GetSerialKey(key);
            if (serialKey == null)
                return false;

            serialKey.IsActive = true;
            return _databaseService.UpdateSerialKey(serialKey);
        }

        /// <summary>
        /// Extends the expiration date of a serial key
        /// </summary>
        public bool ExtendSerialKey(string key, DateTime newExpirationDate)
        {
            if (newExpirationDate <= DateTime.UtcNow)
                return false;

            var serialKey = _databaseService.GetSerialKey(key);
            if (serialKey == null)
                return false;

            serialKey.ExpirationDate = newExpirationDate;
            return _databaseService.UpdateSerialKey(serialKey);
        }

        /// <summary>
        /// Gets detailed information about a serial key
        /// </summary>
        public SerialKeyInfo GetSerialKeyInfo(string key)
        {
            var serialKey = _databaseService.GetSerialKey(key);
            if (serialKey == null)
                return null;

            var userAccount = _databaseService.GetUserAccount(key);

            return new SerialKeyInfo
            {
                SerialKey = serialKey,
                UserAccount = userAccount,
                IsCurrentlyInUse = userAccount?.SessionId != null,
                DaysUntilExpiration = (int)(serialKey.ExpirationDate - DateTime.UtcNow).TotalDays
            };
        }
    }

    /// <summary>
    /// Result of serial key validation
    /// </summary>
    public class SerialKeyValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public SerialKey SerialKey { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public System.Collections.Generic.List<string> ProductAccess { get; set; }
        public bool RequiresHwidReset { get; set; }
        public bool CanResetHwid { get; set; }
        public int RemainingHwidResets { get; set; }
    }

    /// <summary>
    /// Result of HWID reset operation
    /// </summary>
    public class HwidResetResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public string Message { get; set; }
        public int RemainingResets { get; set; }
    }

    /// <summary>
    /// Detailed information about a serial key
    /// </summary>
    public class SerialKeyInfo
    {
        public SerialKey SerialKey { get; set; }
        public UserAccount UserAccount { get; set; }
        public bool IsCurrentlyInUse { get; set; }
        public int DaysUntilExpiration { get; set; }
    }
}
