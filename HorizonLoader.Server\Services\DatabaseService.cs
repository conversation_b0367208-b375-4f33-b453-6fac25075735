using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using HorizonLoader.Shared.Models;

namespace HorizonLoader.Server.Services
{
    /// <summary>
    /// Provides JSON-based data persistence for the server
    /// </summary>
    public class DatabaseService
    {
        private readonly string _databasePath;
        private readonly object _lockObject = new object();
        private Database _database;

        public DatabaseService(string databasePath = "Data\\database.json")
        {
            _databasePath = databasePath;
            InitializeDatabase();
        }

        /// <summary>
        /// Initializes the database and creates necessary directories
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                // Create directory if it doesn't exist
                string directory = Path.GetDirectoryName(_databasePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Load existing database or create new one
                if (File.Exists(_databasePath))
                {
                    LoadDatabase();
                }
                else
                {
                    CreateNewDatabase();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to initialize database: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Loads the database from file
        /// </summary>
        private void LoadDatabase()
        {
            lock (_lockObject)
            {
                try
                {
                    string json = File.ReadAllText(_databasePath);
                    _database = JsonConvert.DeserializeObject<Database>(json) ?? new Database();
                }
                catch (Exception)
                {
                    // If loading fails, create a new database
                    _database = new Database();
                    SaveDatabase();
                }
            }
        }

        /// <summary>
        /// Creates a new empty database
        /// </summary>
        private void CreateNewDatabase()
        {
            _database = new Database();
            SaveDatabase();
        }

        /// <summary>
        /// Saves the database to file
        /// </summary>
        private void SaveDatabase()
        {
            lock (_lockObject)
            {
                try
                {
                    string json = JsonConvert.SerializeObject(_database, Formatting.Indented);
                    File.WriteAllText(_databasePath, json);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to save database: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Saves the database asynchronously
        /// </summary>
        public async Task SaveDatabaseAsync()
        {
            await Task.Run(() => SaveDatabase());
        }

        #region Serial Key Operations

        /// <summary>
        /// Adds a new serial key to the database
        /// </summary>
        public bool AddSerialKey(SerialKey serialKey)
        {
            if (serialKey == null || string.IsNullOrEmpty(serialKey.Key))
                return false;

            lock (_lockObject)
            {
                // Check if key already exists
                if (_database.SerialKeys.Any(k => k.Key.Equals(serialKey.Key, StringComparison.OrdinalIgnoreCase)))
                    return false;

                _database.SerialKeys.Add(serialKey);
                SaveDatabase();
                return true;
            }
        }

        /// <summary>
        /// Gets a serial key by its key value
        /// </summary>
        public SerialKey GetSerialKey(string key)
        {
            if (string.IsNullOrEmpty(key))
                return null;

            lock (_lockObject)
            {
                return _database.SerialKeys.FirstOrDefault(k => k.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
            }
        }

        /// <summary>
        /// Updates an existing serial key
        /// </summary>
        public bool UpdateSerialKey(SerialKey serialKey)
        {
            if (serialKey == null || string.IsNullOrEmpty(serialKey.Key))
                return false;

            lock (_lockObject)
            {
                var existingKey = _database.SerialKeys.FirstOrDefault(k => k.Key.Equals(serialKey.Key, StringComparison.OrdinalIgnoreCase));
                if (existingKey == null)
                    return false;

                // Update properties
                existingKey.ProductId = serialKey.ProductId;
                existingKey.ProductName = serialKey.ProductName;
                existingKey.ExpirationDate = serialKey.ExpirationDate;
                existingKey.IsActive = serialKey.IsActive;
                existingKey.BoundHwid = serialKey.BoundHwid;
                existingKey.CanResetHwid = serialKey.CanResetHwid;
                existingKey.HwidResetCount = serialKey.HwidResetCount;
                existingKey.MaxHwidResets = serialKey.MaxHwidResets;
                existingKey.LastUsedDate = serialKey.LastUsedDate;

                SaveDatabase();
                return true;
            }
        }

        /// <summary>
        /// Deletes a serial key
        /// </summary>
        public bool DeleteSerialKey(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            lock (_lockObject)
            {
                var serialKey = _database.SerialKeys.FirstOrDefault(k => k.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
                if (serialKey == null)
                    return false;

                _database.SerialKeys.Remove(serialKey);
                SaveDatabase();
                return true;
            }
        }

        /// <summary>
        /// Gets all serial keys
        /// </summary>
        public List<SerialKey> GetAllSerialKeys()
        {
            lock (_lockObject)
            {
                return new List<SerialKey>(_database.SerialKeys);
            }
        }

        #endregion

        #region User Account Operations

        /// <summary>
        /// Adds a new user account
        /// </summary>
        public bool AddUserAccount(UserAccount account)
        {
            if (account == null || string.IsNullOrEmpty(account.SerialKey))
                return false;

            lock (_lockObject)
            {
                // Check if account already exists
                if (_database.UserAccounts.Any(u => u.SerialKey.Equals(account.SerialKey, StringComparison.OrdinalIgnoreCase)))
                    return false;

                _database.UserAccounts.Add(account);
                SaveDatabase();
                return true;
            }
        }

        /// <summary>
        /// Gets a user account by serial key
        /// </summary>
        public UserAccount GetUserAccount(string serialKey)
        {
            if (string.IsNullOrEmpty(serialKey))
                return null;

            lock (_lockObject)
            {
                return _database.UserAccounts.FirstOrDefault(u => u.SerialKey.Equals(serialKey, StringComparison.OrdinalIgnoreCase));
            }
        }

        /// <summary>
        /// Updates an existing user account
        /// </summary>
        public bool UpdateUserAccount(UserAccount account)
        {
            if (account == null || string.IsNullOrEmpty(account.SerialKey))
                return false;

            lock (_lockObject)
            {
                var existingAccount = _database.UserAccounts.FirstOrDefault(u => u.SerialKey.Equals(account.SerialKey, StringComparison.OrdinalIgnoreCase));
                if (existingAccount == null)
                    return false;

                // Update properties
                existingAccount.Hwid = account.Hwid;
                existingAccount.IsActive = account.IsActive;
                existingAccount.IsLocked = account.IsLocked;
                existingAccount.LockoutEndTime = account.LockoutEndTime;
                existingAccount.FailedLoginAttempts = account.FailedLoginAttempts;
                existingAccount.LastLoginDate = account.LastLoginDate;
                existingAccount.LastLoginIp = account.LastLoginIp;
                existingAccount.SessionId = account.SessionId;
                existingAccount.SessionStartTime = account.SessionStartTime;
                existingAccount.SessionEndTime = account.SessionEndTime;
                existingAccount.LoginHistory = account.LoginHistory;
                existingAccount.ProductAccess = account.ProductAccess;

                SaveDatabase();
                return true;
            }
        }

        /// <summary>
        /// Deletes a user account
        /// </summary>
        public bool DeleteUserAccount(string serialKey)
        {
            if (string.IsNullOrEmpty(serialKey))
                return false;

            lock (_lockObject)
            {
                var account = _database.UserAccounts.FirstOrDefault(u => u.SerialKey.Equals(serialKey, StringComparison.OrdinalIgnoreCase));
                if (account == null)
                    return false;

                _database.UserAccounts.Remove(account);
                SaveDatabase();
                return true;
            }
        }

        /// <summary>
        /// Gets all user accounts
        /// </summary>
        public List<UserAccount> GetAllUserAccounts()
        {
            lock (_lockObject)
            {
                return new List<UserAccount>(_database.UserAccounts);
            }
        }

        #endregion

        #region Configuration Operations

        /// <summary>
        /// Gets server configuration
        /// </summary>
        public ServerConfiguration GetConfiguration()
        {
            lock (_lockObject)
            {
                return _database.Configuration ?? new ServerConfiguration();
            }
        }

        /// <summary>
        /// Updates server configuration
        /// </summary>
        public bool UpdateConfiguration(ServerConfiguration configuration)
        {
            if (configuration == null)
                return false;

            lock (_lockObject)
            {
                _database.Configuration = configuration;
                SaveDatabase();
                return true;
            }
        }

        #endregion

        /// <summary>
        /// Gets database statistics
        /// </summary>
        public DatabaseStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                return new DatabaseStatistics
                {
                    TotalSerialKeys = _database.SerialKeys.Count,
                    ActiveSerialKeys = _database.SerialKeys.Count(k => k.IsActive && k.IsValid()),
                    ExpiredSerialKeys = _database.SerialKeys.Count(k => k.IsExpired()),
                    TotalUserAccounts = _database.UserAccounts.Count,
                    ActiveUserAccounts = _database.UserAccounts.Count(u => u.IsActive),
                    LockedUserAccounts = _database.UserAccounts.Count(u => u.IsCurrentlyLocked()),
                    LastModified = File.GetLastWriteTime(_databasePath)
                };
            }
        }
    }

    /// <summary>
    /// Represents the database structure
    /// </summary>
    public class Database
    {
        [JsonProperty("serialKeys")]
        public List<SerialKey> SerialKeys { get; set; } = new List<SerialKey>();

        [JsonProperty("userAccounts")]
        public List<UserAccount> UserAccounts { get; set; } = new List<UserAccount>();

        [JsonProperty("configuration")]
        public ServerConfiguration Configuration { get; set; } = new ServerConfiguration();

        [JsonProperty("version")]
        public string Version { get; set; } = "1.0.0";

        [JsonProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [JsonProperty("lastModified")]
        public DateTime LastModified { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Server configuration settings
    /// </summary>
    public class ServerConfiguration
    {
        [JsonProperty("enableAccountLocking")]
        public bool EnableAccountLocking { get; set; } = true;

        [JsonProperty("maxLoginAttempts")]
        public int MaxLoginAttempts { get; set; } = 3;

        [JsonProperty("lockoutDurationMinutes")]
        public int LockoutDurationMinutes { get; set; } = 5;

        [JsonProperty("sessionTimeoutMinutes")]
        public int SessionTimeoutMinutes { get; set; } = 30;

        [JsonProperty("enableLogging")]
        public bool EnableLogging { get; set; } = true;

        [JsonProperty("maintenanceMode")]
        public bool MaintenanceMode { get; set; } = false;

        [JsonProperty("adminPassword")]
        public string AdminPassword { get; set; } = "admin123"; // Should be hashed in production
    }

    /// <summary>
    /// Database statistics
    /// </summary>
    public class DatabaseStatistics
    {
        public int TotalSerialKeys { get; set; }
        public int ActiveSerialKeys { get; set; }
        public int ExpiredSerialKeys { get; set; }
        public int TotalUserAccounts { get; set; }
        public int ActiveUserAccounts { get; set; }
        public int LockedUserAccounts { get; set; }
        public DateTime LastModified { get; set; }
    }
}
