using System;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using System.Linq;

namespace HorizonLoader.Shared.Security
{
    /// <summary>
    /// Generates hardware fingerprints for license binding
    /// </summary>
    public static class HardwareFingerprint
    {
        /// <summary>
        /// Generates a unique hardware ID based on system components
        /// </summary>
        public static string GenerateHwid()
        {
            try
            {
                StringBuilder hwid = new StringBuilder();

                // Get CPU ID
                string cpuId = GetCpuId();
                if (!string.IsNullOrEmpty(cpuId))
                    hwid.Append(cpuId);

                // Get Motherboard serial number
                string motherboardSerial = GetMotherboardSerial();
                if (!string.IsNullOrEmpty(motherboardSerial))
                    hwid.Append(motherboardSerial);

                // Get BIOS serial number
                string biosSerial = GetBiosSerial();
                if (!string.IsNullOrEmpty(biosSerial))
                    hwid.Append(biosSerial);

                // Get MAC address of first network adapter
                string macAddress = GetMacAddress();
                if (!string.IsNullOrEmpty(macAddress))
                    hwid.Append(macAddress);

                // Get Windows installation ID
                string windowsId = GetWindowsId();
                if (!string.IsNullOrEmpty(windowsId))
                    hwid.Append(windowsId);

                // If we couldn't get any hardware info, use machine name as fallback
                if (hwid.Length == 0)
                {
                    hwid.Append(Environment.MachineName);
                    hwid.Append(Environment.UserName);
                }

                // Generate SHA-256 hash of the combined hardware info
                using (var sha256 = SHA256.Create())
                {
                    byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(hwid.ToString()));
                    return Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "").Substring(0, 32);
                }
            }
            catch (Exception)
            {
                // Fallback HWID generation
                return GenerateFallbackHwid();
            }
        }

        /// <summary>
        /// Gets the CPU ID using WMI
        /// </summary>
        private static string GetCpuId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["ProcessorId"]?.ToString();
                    }
                }
            }
            catch (Exception)
            {
                // WMI access might be restricted
            }
            return null;
        }

        /// <summary>
        /// Gets the motherboard serial number using WMI
        /// </summary>
        private static string GetMotherboardSerial()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        string serial = obj["SerialNumber"]?.ToString();
                        if (!string.IsNullOrEmpty(serial) && serial != "To be filled by O.E.M.")
                            return serial;
                    }
                }
            }
            catch (Exception)
            {
                // WMI access might be restricted
            }
            return null;
        }

        /// <summary>
        /// Gets the BIOS serial number using WMI
        /// </summary>
        private static string GetBiosSerial()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        string serial = obj["SerialNumber"]?.ToString();
                        if (!string.IsNullOrEmpty(serial) && serial != "To be filled by O.E.M.")
                            return serial;
                    }
                }
            }
            catch (Exception)
            {
                // WMI access might be restricted
            }
            return null;
        }

        /// <summary>
        /// Gets the MAC address of the first network adapter
        /// </summary>
        private static string GetMacAddress()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT MACAddress FROM Win32_NetworkAdapter WHERE MACAddress IS NOT NULL AND NetConnectionStatus = 2"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        string mac = obj["MACAddress"]?.ToString();
                        if (!string.IsNullOrEmpty(mac))
                            return mac.Replace(":", "").Replace("-", "");
                    }
                }
            }
            catch (Exception)
            {
                // WMI access might be restricted
            }
            return null;
        }

        /// <summary>
        /// Gets Windows installation ID
        /// </summary>
        private static string GetWindowsId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT UUID FROM Win32_ComputerSystemProduct"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        string uuid = obj["UUID"]?.ToString();
                        if (!string.IsNullOrEmpty(uuid) && uuid != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF")
                            return uuid;
                    }
                }
            }
            catch (Exception)
            {
                // WMI access might be restricted
            }
            return null;
        }

        /// <summary>
        /// Generates a fallback HWID when WMI is not available
        /// </summary>
        private static string GenerateFallbackHwid()
        {
            try
            {
                StringBuilder fallback = new StringBuilder();
                
                // Use environment variables as fallback
                fallback.Append(Environment.MachineName);
                fallback.Append(Environment.UserName);
                fallback.Append(Environment.ProcessorCount);
                fallback.Append(Environment.OSVersion.ToString());
                
                // Add some system-specific information
                fallback.Append(Environment.SystemDirectory);
                fallback.Append(Environment.Is64BitOperatingSystem);
                
                using (var sha256 = SHA256.Create())
                {
                    byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallback.ToString()));
                    return Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "").Substring(0, 32);
                }
            }
            catch (Exception)
            {
                // Ultimate fallback - use current timestamp and random data
                var random = new Random();
                var timestamp = DateTime.UtcNow.Ticks;
                return $"FALLBACK{timestamp:X}{random.Next():X}".Substring(0, 32);
            }
        }

        /// <summary>
        /// Validates if a HWID matches the current system
        /// </summary>
        public static bool ValidateHwid(string expectedHwid)
        {
            if (string.IsNullOrEmpty(expectedHwid))
                return false;

            string currentHwid = GenerateHwid();
            return string.Equals(expectedHwid, currentHwid, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets detailed hardware information for debugging purposes
        /// </summary>
        public static string GetHardwareInfo()
        {
            try
            {
                StringBuilder info = new StringBuilder();
                info.AppendLine($"Machine Name: {Environment.MachineName}");
                info.AppendLine($"User Name: {Environment.UserName}");
                info.AppendLine($"OS Version: {Environment.OSVersion}");
                info.AppendLine($"Processor Count: {Environment.ProcessorCount}");
                info.AppendLine($"64-bit OS: {Environment.Is64BitOperatingSystem}");
                info.AppendLine($"CPU ID: {GetCpuId() ?? "N/A"}");
                info.AppendLine($"Motherboard Serial: {GetMotherboardSerial() ?? "N/A"}");
                info.AppendLine($"BIOS Serial: {GetBiosSerial() ?? "N/A"}");
                info.AppendLine($"MAC Address: {GetMacAddress() ?? "N/A"}");
                info.AppendLine($"Windows ID: {GetWindowsId() ?? "N/A"}");
                info.AppendLine($"Generated HWID: {GenerateHwid()}");
                
                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"Error getting hardware info: {ex.Message}";
            }
        }
    }
}
