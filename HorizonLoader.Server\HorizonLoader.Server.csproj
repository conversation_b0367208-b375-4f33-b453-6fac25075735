<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B2C3D4E5-0000-0000-0000-000000000000}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>HorizonLoader.Server</RootNamespace>
    <AssemblyName>HorizonLoader.Server</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\AdminForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AdminForm.Designer.cs">
      <DependentUpon>AdminForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SerialKeyGeneratorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SerialKeyGeneratorForm.Designer.cs">
      <DependentUpon>SerialKeyGeneratorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ConfigurationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ConfigurationForm.Designer.cs">
      <DependentUpon>ConfigurationForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Network\ServerNetworkManager.cs" />
    <Compile Include="Services\UserManagementService.cs" />
    <Compile Include="Services\SerialKeyService.cs" />
    <Compile Include="Services\DatabaseService.cs" />
    <Compile Include="Services\LoggingService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Forms\AdminForm.resx">
      <DependentUpon>AdminForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SerialKeyGeneratorForm.resx">
      <DependentUpon>SerialKeyGeneratorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ConfigurationForm.resx">
      <DependentUpon>ConfigurationForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>

    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HorizonLoader.Shared\HorizonLoader.Shared.csproj">
      <Project>{C3D4E5F6-0000-0000-0000-000000000000}</Project>
      <Name>HorizonLoader.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>