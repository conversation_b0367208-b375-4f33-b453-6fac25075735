using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace HorizonLoader.Shared.Models
{
    /// <summary>
    /// Represents a user account with authentication and session information
    /// </summary>
    public class UserAccount
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("serialKey")]
        public string SerialKey { get; set; }

        [JsonProperty("hwid")]
        public string Hwid { get; set; }

        [JsonProperty("isActive")]
        public bool IsActive { get; set; }

        [JsonProperty("isLocked")]
        public bool IsLocked { get; set; }

        [JsonProperty("lockoutEndTime")]
        public DateTime? LockoutEndTime { get; set; }

        [JsonProperty("failedLoginAttempts")]
        public int FailedLoginAttempts { get; set; }

        [JsonProperty("lastLoginDate")]
        public DateTime? LastLoginDate { get; set; }

        [JsonProperty("lastLoginIp")]
        public string LastLoginIp { get; set; }

        [JsonProperty("createdDate")]
        public DateTime CreatedDate { get; set; }

        [JsonProperty("sessionId")]
        public string SessionId { get; set; }

        [JsonProperty("sessionStartTime")]
        public DateTime? SessionStartTime { get; set; }

        [JsonProperty("sessionEndTime")]
        public DateTime? SessionEndTime { get; set; }

        [JsonProperty("loginHistory")]
        public List<LoginRecord> LoginHistory { get; set; }

        [JsonProperty("productAccess")]
        public List<string> ProductAccess { get; set; }

        public UserAccount()
        {
            Id = Guid.NewGuid().ToString();
            CreatedDate = DateTime.UtcNow;
            IsActive = true;
            IsLocked = false;
            FailedLoginAttempts = 0;
            LoginHistory = new List<LoginRecord>();
            ProductAccess = new List<string>();
        }

        /// <summary>
        /// Checks if the account is currently locked
        /// </summary>
        public bool IsCurrentlyLocked()
        {
            if (!IsLocked)
                return false;

            if (LockoutEndTime.HasValue && DateTime.UtcNow >= LockoutEndTime.Value)
            {
                // Auto-unlock if lockout period has expired
                IsLocked = false;
                LockoutEndTime = null;
                FailedLoginAttempts = 0;
                return false;
            }

            return IsLocked;
        }

        /// <summary>
        /// Locks the account for a specified duration
        /// </summary>
        public void LockAccount(TimeSpan lockoutDuration)
        {
            IsLocked = true;
            LockoutEndTime = DateTime.UtcNow.Add(lockoutDuration);
        }

        /// <summary>
        /// Unlocks the account
        /// </summary>
        public void UnlockAccount()
        {
            IsLocked = false;
            LockoutEndTime = null;
            FailedLoginAttempts = 0;
        }

        /// <summary>
        /// Records a failed login attempt
        /// </summary>
        public void RecordFailedLogin(int maxAttempts, TimeSpan lockoutDuration)
        {
            FailedLoginAttempts++;
            
            if (FailedLoginAttempts >= maxAttempts)
            {
                LockAccount(lockoutDuration);
            }
        }

        /// <summary>
        /// Records a successful login
        /// </summary>
        public void RecordSuccessfulLogin(string ipAddress)
        {
            LastLoginDate = DateTime.UtcNow;
            LastLoginIp = ipAddress;
            FailedLoginAttempts = 0;
            SessionId = Guid.NewGuid().ToString();
            SessionStartTime = DateTime.UtcNow;

            // Add to login history
            LoginHistory.Add(new LoginRecord
            {
                LoginTime = DateTime.UtcNow,
                IpAddress = ipAddress,
                Success = true
            });

            // Keep only last 50 login records
            if (LoginHistory.Count > 50)
            {
                LoginHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// Ends the current session
        /// </summary>
        public void EndSession()
        {
            SessionEndTime = DateTime.UtcNow;
            SessionId = null;
        }

        /// <summary>
        /// Checks if the user has access to a specific product
        /// </summary>
        public bool HasProductAccess(string productId)
        {
            return ProductAccess.Contains(productId);
        }

        /// <summary>
        /// Grants access to a product
        /// </summary>
        public void GrantProductAccess(string productId)
        {
            if (!ProductAccess.Contains(productId))
            {
                ProductAccess.Add(productId);
            }
        }

        /// <summary>
        /// Revokes access to a product
        /// </summary>
        public void RevokeProductAccess(string productId)
        {
            ProductAccess.Remove(productId);
        }
    }

    /// <summary>
    /// Represents a login record for audit purposes
    /// </summary>
    public class LoginRecord
    {
        [JsonProperty("loginTime")]
        public DateTime LoginTime { get; set; }

        [JsonProperty("ipAddress")]
        public string IpAddress { get; set; }

        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("failureReason")]
        public string FailureReason { get; set; }
    }
}
