using System;
using System.IO;
using System.Threading.Tasks;

namespace HorizonLoader.Server.Services
{
    /// <summary>
    /// Provides logging functionality for the server application
    /// </summary>
    public class LoggingService
    {
        private readonly string _logPath;
        private readonly bool _enableLogging;
        private readonly LogLevel _logLevel;
        private readonly object _lockObject = new object();

        public LoggingService(string logPath = "Logs\\server.log", bool enableLogging = true, LogLevel logLevel = LogLevel.Info)
        {
            _logPath = logPath;
            _enableLogging = enableLogging;
            _logLevel = logLevel;

            if (_enableLogging)
            {
                InitializeLogging();
            }
        }

        /// <summary>
        /// Initializes the logging system
        /// </summary>
        private void InitializeLogging()
        {
            try
            {
                string directory = Path.GetDirectoryName(_logPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Write startup message
                WriteLog(LogLevel.Info, "Logging service initialized");
            }
            catch (Exception ex)
            {
                // If we can't initialize logging, write to event log or console
                Console.WriteLine($"Failed to initialize logging: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs an informational message
        /// </summary>
        public void LogInfo(string message)
        {
            WriteLog(LogLevel.Info, message);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        public void LogWarning(string message)
        {
            WriteLog(LogLevel.Warning, message);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        public void LogError(string message)
        {
            WriteLog(LogLevel.Error, message);
        }

        /// <summary>
        /// Logs an error with exception details
        /// </summary>
        public void LogError(string message, Exception exception)
        {
            string fullMessage = $"{message} - Exception: {exception.Message}";
            if (exception.StackTrace != null)
            {
                fullMessage += $"\nStack Trace: {exception.StackTrace}";
            }
            WriteLog(LogLevel.Error, fullMessage);
        }

        /// <summary>
        /// Logs a debug message
        /// </summary>
        public void LogDebug(string message)
        {
            WriteLog(LogLevel.Debug, message);
        }

        /// <summary>
        /// Logs a security-related event
        /// </summary>
        public void LogSecurityEvent(string message)
        {
            WriteLog(LogLevel.Security, $"SECURITY: {message}");
        }

        /// <summary>
        /// Logs a network-related event
        /// </summary>
        public void LogNetworkEvent(string message)
        {
            WriteLog(LogLevel.Info, $"NETWORK: {message}");
        }

        /// <summary>
        /// Logs an authentication event
        /// </summary>
        public void LogAuthenticationEvent(string message)
        {
            WriteLog(LogLevel.Info, $"AUTH: {message}");
        }

        /// <summary>
        /// Writes a log entry with the specified level
        /// </summary>
        private void WriteLog(LogLevel level, string message)
        {
            if (!_enableLogging || level < _logLevel)
                return;

            try
            {
                lock (_lockObject)
                {
                    string logEntry = FormatLogEntry(level, message);
                    File.AppendAllText(_logPath, logEntry + Environment.NewLine);
                }
            }
            catch (Exception)
            {
                // Silently handle logging errors to prevent application crashes
            }
        }

        /// <summary>
        /// Writes a log entry asynchronously
        /// </summary>
        public async Task WriteLogAsync(LogLevel level, string message)
        {
            if (!_enableLogging || level < _logLevel)
                return;

            try
            {
                string logEntry = FormatLogEntry(level, message);
                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        File.AppendAllText(_logPath, logEntry + Environment.NewLine);
                    }
                });
            }
            catch (Exception)
            {
                // Silently handle logging errors
            }
        }

        /// <summary>
        /// Formats a log entry with timestamp and level
        /// </summary>
        private string FormatLogEntry(LogLevel level, string message)
        {
            return $"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss.fff} UTC] [{level.ToString().ToUpper()}] {message}";
        }

        /// <summary>
        /// Rotates log files if they become too large
        /// </summary>
        public void RotateLogIfNeeded(long maxSizeBytes = 10 * 1024 * 1024) // 10MB default
        {
            try
            {
                if (!File.Exists(_logPath))
                    return;

                var fileInfo = new FileInfo(_logPath);
                if (fileInfo.Length > maxSizeBytes)
                {
                    string backupPath = _logPath.Replace(".log", $"_{DateTime.UtcNow:yyyyMMdd_HHmmss}.log");
                    File.Move(_logPath, backupPath);
                    
                    WriteLog(LogLevel.Info, $"Log file rotated. Backup created: {Path.GetFileName(backupPath)}");
                }
            }
            catch (Exception ex)
            {
                WriteLog(LogLevel.Error, $"Failed to rotate log file: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleans up old log files
        /// </summary>
        public void CleanupOldLogs(int daysToKeep = 30)
        {
            try
            {
                string directory = Path.GetDirectoryName(_logPath);
                if (string.IsNullOrEmpty(directory) || !Directory.Exists(directory))
                    return;

                string filePattern = Path.GetFileNameWithoutExtension(_logPath) + "_*.log";
                var oldFiles = Directory.GetFiles(directory, filePattern);
                var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
                int deletedCount = 0;

                foreach (string file in oldFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTimeUtc < cutoffDate)
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                }

                if (deletedCount > 0)
                {
                    WriteLog(LogLevel.Info, $"Cleaned up {deletedCount} old log files");
                }
            }
            catch (Exception ex)
            {
                WriteLog(LogLevel.Error, $"Failed to cleanup old logs: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current log file size
        /// </summary>
        public long GetLogFileSize()
        {
            try
            {
                if (File.Exists(_logPath))
                {
                    return new FileInfo(_logPath).Length;
                }
            }
            catch (Exception)
            {
                // Ignore errors
            }
            return 0;
        }

        /// <summary>
        /// Gets the last few lines from the log file
        /// </summary>
        public string[] GetRecentLogEntries(int lineCount = 100)
        {
            try
            {
                if (!File.Exists(_logPath))
                    return new string[0];

                var lines = File.ReadAllLines(_logPath);
                if (lines.Length <= lineCount)
                    return lines;

                var recentLines = new string[lineCount];
                Array.Copy(lines, lines.Length - lineCount, recentLines, 0, lineCount);
                return recentLines;
            }
            catch (Exception)
            {
                return new string[] { "Error reading log file" };
            }
        }

        /// <summary>
        /// Flushes any pending log entries
        /// </summary>
        public void Flush()
        {
            // Since we're using synchronous file operations, this is a no-op
            // But it's here for interface compatibility
        }

        /// <summary>
        /// Disposes of the logging service
        /// </summary>
        public void Dispose()
        {
            try
            {
                WriteLog(LogLevel.Info, "Logging service shutting down");
            }
            catch (Exception)
            {
                // Ignore errors during disposal
            }
        }
    }

    /// <summary>
    /// Log levels for filtering log messages
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        Security = 4
    }
}
