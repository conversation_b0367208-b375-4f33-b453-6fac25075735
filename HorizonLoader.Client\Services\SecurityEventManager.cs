using System;
using System.Diagnostics;
using System.Net;
using System.Threading.Tasks;
using HorizonLoader.Client.Network;
using HorizonLoader.Shared.Models;
using HorizonLoader.Shared.Network;
using HorizonLoader.Shared.Security;

namespace HorizonLoader.Client.Services
{
    /// <summary>
    /// Centralised manager that handles responses to security detections (anti-debug, tampering, etc.)
    /// </summary>
    public static class SecurityEventManager
    {
        private static ClientNetworkManager _networkManager;
        private static string _serialKey;
        private static string _hwid;
        private static bool _isAuthenticated;
        private static readonly object _lock = new object();


        /// <summary>
        /// Must be called as early as possible after the <see cref="ClientNetworkManager"/> is created.
        /// </summary>
        public static void Initialize(ClientNetworkManager networkManager)
        {
            if (networkManager == null) throw new ArgumentNullException(nameof(networkManager));

            lock (_lock)
            {
                if (_networkManager != null)
                    return; // already initialised

                _networkManager = networkManager;

                // Subscribe to anti-debug callbacks
                AntiDebug.TamperingDetected += OnTamperingDetected;
            }
        }

        /// <summary>
        /// Should be invoked by the authentication layer right after a successful login so that we
        /// can include the serial key in post-authentication alerts.
        /// </summary>
        public static void SetAuthenticationInfo(string serialKey, string hwid)
        {
            lock (_lock)
            {
                _serialKey = serialKey;
                _hwid = hwid;
                _isAuthenticated = true;
            }
        }

        /// <summary>
        /// Callback raised from <see cref="AntiDebug"/> when a detection occurs.
        /// </summary>
        private static async void OnTamperingDetected(string detectionType)
        {
            try
            {
                string hwid = _hwid ?? HardwareFingerprint.GenerateHwid();
                string clientIp = await GetPublicIpAsync();

                var alert = new SecurityAlert
                {
                    SerialKey = _isAuthenticated ? _serialKey : null,
                    Hwid = hwid,
                    DetectionType = detectionType,
                    Timestamp = DateTime.UtcNow,
                    ClientIp = clientIp
                };


                // Attempt to notify server if we have a network connection
                if (_networkManager != null && _networkManager.IsConnected)
                {
                    var packetType = _isAuthenticated ? PacketType.SecurityAlert : PacketType.BlacklistHwidRequest;
                    var packet = new NetworkPacket(packetType, alert, _networkManager.SessionId);
                    await _networkManager.SendPacketAsync(packet);
                }
            }
            catch (Exception ex)
            {
                // If anything fails, write to debug output so that we have *some* trace.
                Debug.WriteLine($"SecurityEventManager failed to process tampering detection: {ex.Message}");
            }
            finally
            {
                // Terminate silently after sending alert
                Environment.Exit(-1);
            }
        }

        #region Helpers

        private static async Task<string> GetPublicIpAsync()
        {
            try
            {
                using (var wc = new WebClient())
                {
                    wc.Proxy = null;
                    string ip = await wc.DownloadStringTaskAsync(new Uri("https://api.ipify.org"));
                    return ip;
                }
            }
            catch
            {
                return "Unknown";
            }
        }

        #endregion
    }
}