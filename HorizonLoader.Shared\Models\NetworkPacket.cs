using System;
using System.Text;
using Newtonsoft.Json;
using HorizonLoader.Shared.Network;

namespace HorizonLoader.Shared.Models
{
    /// <summary>
    /// Represents a network packet for client-server communication
    /// </summary>
    public class NetworkPacket
    {
        [JsonProperty("packetType")]
        public PacketType PacketType { get; set; }

        [JsonProperty("sessionId")]
        public string SessionId { get; set; }

        [JsonProperty("timestamp")]
        public DateTime Timestamp { get; set; }

        [JsonProperty("data")]
        public string Data { get; set; }

        [JsonProperty("checksum")]
        public string Checksum { get; set; }

        public NetworkPacket()
        {
            Timestamp = DateTime.UtcNow;
        }

        public NetworkPacket(PacketType packetType, object data = null, string sessionId = null)
        {
            PacketType = packetType;
            SessionId = sessionId;
            Timestamp = DateTime.UtcNow;
            
            if (data != null)
            {
                Data = JsonConvert.SerializeObject(data);
            }
        }

        /// <summary>
        /// Serializes the packet to JSON string
        /// </summary>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }

        /// <summary>
        /// Deserializes a JSON string to NetworkPacket
        /// </summary>
        public static NetworkPacket FromJson(string json)
        {
            try
            {
                return JsonConvert.DeserializeObject<NetworkPacket>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }

        /// <summary>
        /// Gets the data as a specific type
        /// </summary>
        public T GetData<T>() where T : class
        {
            if (string.IsNullOrEmpty(Data))
                return null;

            try
            {
                return JsonConvert.DeserializeObject<T>(Data);
            }
            catch (JsonException)
            {
                return null;
            }
        }

        /// <summary>
        /// Sets the data from an object
        /// </summary>
        public void SetData<T>(T data) where T : class
        {
            if (data != null)
            {
                Data = JsonConvert.SerializeObject(data);
            }
            else
            {
                Data = null;
            }
        }

        /// <summary>
        /// Converts the packet to bytes for network transmission
        /// </summary>
        public byte[] ToBytes()
        {
            string json = ToJson();
            return Encoding.UTF8.GetBytes(json);
        }

        /// <summary>
        /// Creates a NetworkPacket from bytes
        /// </summary>
        public static NetworkPacket FromBytes(byte[] bytes)
        {
            try
            {
                string json = Encoding.UTF8.GetString(bytes);
                return FromJson(json);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Calculates and sets the checksum for the packet
        /// </summary>
        public void CalculateChecksum()
        {
            string content = $"{PacketType}{SessionId}{Timestamp:O}{Data}";
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(content));
                Checksum = Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Validates the packet checksum
        /// </summary>
        public bool ValidateChecksum()
        {
            if (string.IsNullOrEmpty(Checksum))
                return false;

            string originalChecksum = Checksum;
            CalculateChecksum();
            bool isValid = originalChecksum == Checksum;
            Checksum = originalChecksum; // Restore original checksum
            return isValid;
        }
    }
}
