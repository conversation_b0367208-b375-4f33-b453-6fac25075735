# HorizonLoader - Secure Client-Server Application

A comprehensive C# client-server application built with Windows Forms and .NET Framework 4.7.2, featuring advanced security mechanisms, hardware-based license protection, and encrypted TCP communication.

## 🚀 Features

### Architecture & Communication
- **TCP-based client-server architecture** for Windows
- **AES-256 encrypted TCP communication** to prevent tampering and eavesdropping
- **JSON-based data persistence** using Newtonsoft.Json
- **Real-time heartbeat monitoring** for connection health
- **Async/await patterns** for non-blocking network operations

### Authentication & Security
- **Serial key-based authentication** system (no user registration required)
- **Hardware ID (HWID) binding** for license protection using system fingerprinting
- **Anti-debugging protection** using Windows API calls
- **Anti-tampering mechanisms** for executable integrity
- **Hardware fingerprinting** using CPU ID, motherboard serial, MAC address, etc.
- **Secure key derivation** using PBKDF2 for encryption keys

### License Management
- **Admin-only serial key generation** with configurable properties:
  - Product ID and name
  - Expiration date/time
  - HWID reset capability
  - Maximum reset count
- **Configurable account locking** when same serial key is used on different hardware
- **Automatic license expiration handling** with timer-based checks
- **License validation** with real-time status monitoring

### User Interface
- **Windows Forms-based GUI** for both client and server applications
- **Server Admin Panel** with:
  - Serial key generation and management
  - HWID reset functionality
  - User account management grid
  - Configuration settings
  - Real-time statistics dashboard
  - Activity logging and monitoring
- **Client Interface** with:
  - Secure login form
  - License status monitoring
  - Hardware information display
  - Activity logging

### Additional Features
- **Comprehensive logging system** for security events and user activities
- **Configuration management** using App.config
- **Database statistics** and health monitoring
- **Graceful error handling** with user-friendly feedback
- **Single instance enforcement** for both client and server
- **Resource cleanup** and proper disposal patterns

## 🏗️ Project Structure

```
HorizonLoader/
├── HorizonLoader.sln                 # Visual Studio Solution
├── HorizonLoader.Shared/             # Shared Library
│   ├── Models/                       # Data models
│   │   ├── SerialKey.cs
│   │   ├── UserAccount.cs
│   │   ├── NetworkPacket.cs
│   │   ├── AuthenticationRequest.cs
│   │   └── AuthenticationResponse.cs
│   ├── Network/                      # Network protocol
│   │   ├── NetworkProtocol.cs
│   │   └── PacketType.cs
│   └── Security/                     # Security components
│       ├── EncryptionHelper.cs
│       ├── HardwareFingerprint.cs
│       └── AntiDebug.cs
├── HorizonLoader.Server/             # Server Application
│   ├── Forms/                        # Windows Forms
│   │   ├── AdminForm.cs
│   │   ├── SerialKeyGeneratorForm.cs
│   │   └── ConfigurationForm.cs
│   ├── Services/                     # Business logic
│   │   ├── DatabaseService.cs
│   │   ├── SerialKeyService.cs
│   │   ├── UserManagementService.cs
│   │   └── LoggingService.cs
│   ├── Network/                      # Network management
│   │   └── ServerNetworkManager.cs
│   └── Program.cs                    # Entry point
└── HorizonLoader.Client/             # Client Application
    ├── Forms/                        # Windows Forms
    │   ├── LoginForm.cs
    │   └── MainForm.cs
    ├── Services/                     # Client services
    │   ├── AuthenticationService.cs
    │   └── LicenseService.cs
    ├── Network/                      # Network management
    │   └── ClientNetworkManager.cs
    └── Program.cs                    # Entry point
```

## 🛠️ Technical Requirements

- **.NET Framework 4.7.2** or higher
- **Visual Studio 2019** or later
- **Windows OS** (Windows 7 SP1 or later)
- **NuGet Packages:**
  - Newtonsoft.Json 13.0.3

## 🚀 Getting Started

### Building the Solution

1. **Clone or download** the project files
2. **Open** `HorizonLoader.sln` in Visual Studio
3. **Restore NuGet packages** (should happen automatically)
4. **Build the solution** (Ctrl+Shift+B)

### Running the Server

1. **Set** `HorizonLoader.Server` as startup project
2. **Run** the server application (F5)
3. **Start the server** from the admin panel
4. **Generate serial keys** using the Serial Key Generator

### Running the Client

1. **Set** `HorizonLoader.Client` as startup project
2. **Run** the client application (F5)
3. **Enter a valid serial key** to authenticate
4. **Monitor license status** in the main window

## ⚙️ Configuration

### Server Configuration (App.config)
```xml
<appSettings>
    <add key="ListenPort" value="8080" />
    <add key="MaxConnections" value="100" />
    <add key="DatabasePath" value="Data\database.json" />
    <add key="EnableAccountLocking" value="true" />
    <add key="MaxLoginAttempts" value="3" />
    <add key="LockoutDuration" value="300000" />
    <add key="EnableLogging" value="true" />
    <add key="LogPath" value="Logs\server.log" />
</appSettings>
```

### Client Configuration (App.config)
```xml
<appSettings>
    <add key="ServerAddress" value="127.0.0.1" />
    <add key="ServerPort" value="8080" />
    <add key="ConnectionTimeout" value="30000" />
    <add key="HeartbeatInterval" value="60000" />
    <add key="EnableLogging" value="true" />
</appSettings>
```

## 🔒 Security Features

### Network Security
- **AES-256 encryption** for all TCP communication
- **Packet integrity verification** using checksums
- **Protocol version validation**
- **Connection timeout handling**

### Anti-Tampering Protection
- **Anti-debugging detection** using multiple methods
- **Process monitoring** for debugging tools
- **Assembly integrity validation**
- **Timing attack detection**

### License Protection
- **Hardware fingerprinting** using system-specific identifiers
- **HWID binding** with configurable reset limits
- **Serial key validation** with expiration handling
- **Account locking** for suspicious activity

## 📊 Database Schema

The server uses JSON-based storage with the following structure:

```json
{
  "serialKeys": [
    {
      "id": "guid",
      "key": "XXXX-XXXX-XXXX-XXXX",
      "productId": "string",
      "productName": "string",
      "createdDate": "datetime",
      "expirationDate": "datetime",
      "isActive": true,
      "boundHwid": "string",
      "canResetHwid": true,
      "hwidResetCount": 0,
      "maxHwidResets": 3
    }
  ],
  "userAccounts": [
    {
      "id": "guid",
      "serialKey": "string",
      "hwid": "string",
      "isActive": true,
      "isLocked": false,
      "lastLoginDate": "datetime",
      "sessionId": "string"
    }
  ],
  "configuration": {
    "enableAccountLocking": true,
    "maxLoginAttempts": 3,
    "lockoutDurationMinutes": 5,
    "sessionTimeoutMinutes": 30
  }
}
```

## 🔧 Development Notes

### Best Practices Implemented
- **Proper resource disposal** using `using` statements and `IDisposable`
- **Async/await patterns** for network operations
- **Exception handling** with user-friendly error messages
- **Logging** for debugging and security monitoring
- **Configuration management** for easy deployment
- **Single responsibility principle** in service classes

### Security Considerations
- **Never store passwords in plain text** (use hashing in production)
- **Validate all user input** before processing
- **Use secure random number generation** for keys
- **Implement rate limiting** for authentication attempts
- **Regular security audits** of the codebase

## 📝 License

This project is provided as-is for educational and development purposes. Please ensure compliance with all applicable laws and regulations when using this software.

## 🤝 Contributing

This is a demonstration project. For production use, consider:
- **Code review** and security audit
- **Unit testing** implementation
- **Performance optimization**
- **Additional security hardening**
- **Database encryption** for sensitive data

## 📞 Support

For questions or issues, please refer to the code documentation and comments within the source files.
