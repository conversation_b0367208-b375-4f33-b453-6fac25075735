using System;
using System.Collections.Generic;
using System.Linq;
using HorizonLoader.Shared.Models;

namespace HorizonLoader.Server.Services
{
    /// <summary>
    /// Provides user account management and authentication functionality
    /// </summary>
    public class UserManagementService
    {
        private readonly DatabaseService _databaseService;
        private readonly SerialKeyService _serialKeyService;
        private readonly LoggingService _loggingService;

        // In-memory HWID blacklist (could be persisted to database in future)
        private readonly HashSet<string> _blacklistedHwids = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        public UserManagementService(DatabaseService databaseService, SerialKeyService serialKeyService, LoggingService loggingService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _serialKeyService = serialKeyService ?? throw new ArgumentNullException(nameof(serialKeyService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Authenticates a user with serial key and HWID
        /// </summary>
        public AuthenticationResponse AuthenticateUser(string serialKey, string hwid, string ipAddress)
        {
            try
            {
                if (string.IsNullOrEmpty(serialKey))
                    return AuthenticationResponse.Failure(AuthenticationResult.InvalidSerialKey, "Serial key is required");

                if (string.IsNullOrEmpty(hwid))
                    return AuthenticationResponse.Failure(AuthenticationResult.ServerError, "Hardware ID is required");

                // Check HWID blacklist
                if (IsHwidBlacklisted(hwid))
                {
                    _loggingService.LogSecurityEvent($"Blacklisted HWID {hwid} attempted authentication from IP {ipAddress}");
                    return AuthenticationResponse.Failure(AuthenticationResult.AccountLocked, "HWID blacklisted");
                }

                // Check if server is in maintenance mode
                var config = _databaseService.GetConfiguration();
                if (config.MaintenanceMode)
                    return AuthenticationResponse.Failure(AuthenticationResult.MaintenanceMode, "Server is currently in maintenance mode");

                // Validate serial key
                var validationResult = _serialKeyService.ValidateSerialKey(serialKey, hwid);
                if (!validationResult.IsValid)
                {
                    _loggingService.LogSecurityEvent($"Failed authentication attempt for serial key {serialKey} from IP {ipAddress}: {validationResult.ErrorMessage}");
                    
                    if (validationResult.RequiresHwidReset)
                    {
                        return AuthenticationResponse.HwidMismatch(validationResult.CanResetHwid, validationResult.RemainingHwidResets);
                    }

                    return AuthenticationResponse.Failure(
                        validationResult.ErrorMessage.Contains("expired") ? AuthenticationResult.ExpiredSerialKey : AuthenticationResult.InvalidSerialKey,
                        validationResult.ErrorMessage);
                }

                // Get or create user account
                var userAccount = GetOrCreateUserAccount(serialKey, hwid, validationResult.SerialKey);

                // Check if account is locked
                if (userAccount.IsCurrentlyLocked())
                {
                    _loggingService.LogSecurityEvent($"Authentication attempt for locked account {serialKey} from IP {ipAddress}");
                    return AuthenticationResponse.AccountLocked(userAccount.LockoutEndTime);
                }

                // Record successful login
                userAccount.RecordSuccessfulLogin(ipAddress);
                userAccount.ProductAccess = validationResult.ProductAccess;
                _databaseService.UpdateUserAccount(userAccount);

                _loggingService.LogInfo($"Successful authentication for serial key {serialKey} from IP {ipAddress}");

                return AuthenticationResponse.Success(
                    userAccount.SessionId,
                    validationResult.ExpirationDate,
                    validationResult.ProductAccess);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Authentication error for serial key {serialKey}: {ex.Message}");
                return AuthenticationResponse.Failure(AuthenticationResult.ServerError, "Internal server error");
            }
        }

        /// <summary>
        /// Gets or creates a user account for the given serial key
        /// </summary>
        private UserAccount GetOrCreateUserAccount(string serialKey, string hwid, SerialKey serialKeyInfo)
        {
            var userAccount = _databaseService.GetUserAccount(serialKey);
            
            if (userAccount == null)
            {
                // Create new user account
                userAccount = new UserAccount
                {
                    SerialKey = serialKey,
                    Hwid = hwid
                };
                
                // Grant access to the product
                userAccount.GrantProductAccess(serialKeyInfo.ProductId);
                
                _databaseService.AddUserAccount(userAccount);
                _loggingService.LogInfo($"Created new user account for serial key {serialKey}");
            }
            else
            {
                // Update HWID if it has changed (after HWID reset)
                if (userAccount.Hwid != hwid)
                {
                    userAccount.Hwid = hwid;
                    _loggingService.LogInfo($"Updated HWID for user account {serialKey}");
                }
            }

            return userAccount;
        }

        /// <summary>
        /// Validates an active session
        /// </summary>
        public bool ValidateSession(string sessionId, string serialKey)
        {
            if (string.IsNullOrEmpty(sessionId) || string.IsNullOrEmpty(serialKey))
                return false;

            var userAccount = _databaseService.GetUserAccount(serialKey);
            if (userAccount == null)
                return false;

            // Check if session ID matches and session is not expired
            if (userAccount.SessionId != sessionId)
                return false;

            if (!userAccount.SessionStartTime.HasValue)
                return false;

            var config = _databaseService.GetConfiguration();
            var sessionTimeout = TimeSpan.FromMinutes(config.SessionTimeoutMinutes);
            
            if (DateTime.UtcNow - userAccount.SessionStartTime.Value > sessionTimeout)
            {
                // Session expired
                EndSession(serialKey);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Ends a user session
        /// </summary>
        public bool EndSession(string serialKey)
        {
            var userAccount = _databaseService.GetUserAccount(serialKey);
            if (userAccount == null)
                return false;

            userAccount.EndSession();
            _databaseService.UpdateUserAccount(userAccount);
            
            _loggingService.LogInfo($"Session ended for serial key {serialKey}");
            return true;
        }

        /// <summary>
        /// Records a failed authentication attempt
        /// </summary>
        public void RecordFailedAuthentication(string serialKey, string ipAddress, string reason)
        {
            try
            {
                var userAccount = _databaseService.GetUserAccount(serialKey);
                if (userAccount != null)
                {
                    var config = _databaseService.GetConfiguration();
                    if (config.EnableAccountLocking)
                    {
                        var lockoutDuration = TimeSpan.FromMinutes(config.LockoutDurationMinutes);
                        userAccount.RecordFailedLogin(config.MaxLoginAttempts, lockoutDuration);
                        _databaseService.UpdateUserAccount(userAccount);
                    }
                }

                _loggingService.LogSecurityEvent($"Failed authentication for serial key {serialKey} from IP {ipAddress}: {reason}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error recording failed authentication: {ex.Message}");
            }
        }

        /// <summary>
        /// Unlocks a user account
        /// </summary>
        public bool UnlockUserAccount(string serialKey)
        {
            var userAccount = _databaseService.GetUserAccount(serialKey);
            if (userAccount == null)
                return false;

            userAccount.UnlockAccount();
            _databaseService.UpdateUserAccount(userAccount);
            
            _loggingService.LogInfo($"User account unlocked for serial key {serialKey}");
            return true;
        }

        /// <summary>
        /// Locks a user account
        /// </summary>
        public bool LockUserAccount(string serialKey, TimeSpan? lockoutDuration = null)
        {
            var userAccount = _databaseService.GetUserAccount(serialKey);
            if (userAccount == null)
                return false;

            var duration = lockoutDuration ?? TimeSpan.FromMinutes(_databaseService.GetConfiguration().LockoutDurationMinutes);
            userAccount.LockAccount(duration);
            _databaseService.UpdateUserAccount(userAccount);
            
            _loggingService.LogInfo($"User account locked for serial key {serialKey}");
            return true;
        }

        /// <summary>
        /// Gets all user accounts with optional filtering
        /// </summary>
        public List<UserAccountInfo> GetUserAccounts(bool activeOnly = false, bool lockedOnly = false)
        {
            var accounts = _databaseService.GetAllUserAccounts();
            
            if (activeOnly)
                accounts = accounts.Where(a => a.IsActive).ToList();
                
            if (lockedOnly)
                accounts = accounts.Where(a => a.IsCurrentlyLocked()).ToList();

            return accounts.Select(account => new UserAccountInfo
            {
                SerialKey = account.SerialKey,
                Hwid = account.Hwid,
                IsActive = account.IsActive,
                IsLocked = account.IsCurrentlyLocked(),
                LockoutEndTime = account.LockoutEndTime,
                FailedLoginAttempts = account.FailedLoginAttempts,
                LastLoginDate = account.LastLoginDate,
                LastLoginIp = account.LastLoginIp,
                CreatedDate = account.CreatedDate,
                IsCurrentlyOnline = !string.IsNullOrEmpty(account.SessionId),
                ProductAccess = account.ProductAccess,
                SerialKeyInfo = _serialKeyService.GetSerialKeyInfo(account.SerialKey)?.SerialKey
            }).ToList();
        }

        /// <summary>
        /// Gets user account statistics
        /// </summary>
        public UserAccountStatistics GetUserAccountStatistics()
        {
            var accounts = _databaseService.GetAllUserAccounts();
            var now = DateTime.UtcNow;
            
            return new UserAccountStatistics
            {
                TotalAccounts = accounts.Count,
                ActiveAccounts = accounts.Count(a => a.IsActive),
                LockedAccounts = accounts.Count(a => a.IsCurrentlyLocked()),
                OnlineAccounts = accounts.Count(a => !string.IsNullOrEmpty(a.SessionId)),
                AccountsCreatedToday = accounts.Count(a => a.CreatedDate.Date == now.Date),
                AccountsLoggedInToday = accounts.Count(a => a.LastLoginDate?.Date == now.Date),
                AverageSessionDuration = CalculateAverageSessionDuration(accounts)
            };
        }

        /// <summary>
        /// Calculates average session duration
        /// </summary>
        private TimeSpan CalculateAverageSessionDuration(List<UserAccount> accounts)
        {
            var completedSessions = accounts
                .Where(a => a.SessionStartTime.HasValue && a.SessionEndTime.HasValue)
                .Select(a => a.SessionEndTime.Value - a.SessionStartTime.Value)
                .ToList();

            if (!completedSessions.Any())
                return TimeSpan.Zero;

            var totalTicks = completedSessions.Sum(ts => ts.Ticks);
            return new TimeSpan(totalTicks / completedSessions.Count);
        }

        /// <summary>
        /// Adds a HWID to the permanent blacklist.
        /// </summary>
        public bool AddHwidToBlacklist(string hwid)
        {
            if (string.IsNullOrWhiteSpace(hwid))
                return false;

            lock (_blacklistedHwids)
            {
                bool added = _blacklistedHwids.Add(hwid);
                if (added)
                {
                    _loggingService.LogSecurityEvent($"HWID {hwid} added to blacklist");
                }
                return added;
            }
        }

        /// <summary>
        /// Checks if a HWID is blacklisted.
        /// </summary>
        public bool IsHwidBlacklisted(string hwid)
        {
            if (string.IsNullOrWhiteSpace(hwid))
                return false;
            lock (_blacklistedHwids)
            {
                return _blacklistedHwids.Contains(hwid);
            }
        }

        /// <summary>
        /// Cleans up expired sessions
        /// </summary>
        public int CleanupExpiredSessions()
        {
            var accounts = _databaseService.GetAllUserAccounts();
            var config = _databaseService.GetConfiguration();
            var sessionTimeout = TimeSpan.FromMinutes(config.SessionTimeoutMinutes);
            var now = DateTime.UtcNow;
            int cleanedUp = 0;

            foreach (var account in accounts)
            {
                if (!string.IsNullOrEmpty(account.SessionId) && 
                    account.SessionStartTime.HasValue &&
                    now - account.SessionStartTime.Value > sessionTimeout)
                {
                    account.EndSession();
                    _databaseService.UpdateUserAccount(account);
                    cleanedUp++;
                }
            }

            if (cleanedUp > 0)
            {
                _loggingService.LogInfo($"Cleaned up {cleanedUp} expired sessions");
            }

            return cleanedUp;
        }
    }

    /// <summary>
    /// User account information for display purposes
    /// </summary>
    public class UserAccountInfo
    {
        public string SerialKey { get; set; }
        public string Hwid { get; set; }
        public bool IsActive { get; set; }
        public bool IsLocked { get; set; }
        public DateTime? LockoutEndTime { get; set; }
        public int FailedLoginAttempts { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public string LastLoginIp { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsCurrentlyOnline { get; set; }
        public List<string> ProductAccess { get; set; }
        public SerialKey SerialKeyInfo { get; set; }
    }

    /// <summary>
    /// User account statistics
    /// </summary>
    public class UserAccountStatistics
    {
        public int TotalAccounts { get; set; }
        public int ActiveAccounts { get; set; }
        public int LockedAccounts { get; set; }
        public int OnlineAccounts { get; set; }
        public int AccountsCreatedToday { get; set; }
        public int AccountsLoggedInToday { get; set; }
        public TimeSpan AverageSessionDuration { get; set; }
    }
}
