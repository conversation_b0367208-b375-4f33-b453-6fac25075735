<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>
    <appSettings>
        <add key="ListenPort" value="8080" />
        <!-- Shared symmetric encryption key (Base64) -->
        <add key="EncryptionKey" value="AqJ4s9Mz7f6+eXgk+7dZ03sG3E1Dlzfz2zoJ3k0hqws=" />
        <add key="MaxConnections" value="100" />
        <add key="DatabasePath" value="Data\database.json" />
        <add key="EnableAccountLocking" value="true" />
        <add key="MaxLoginAttempts" value="3" />
        <add key="LockoutDuration" value="300000" />
        <add key="EnableLogging" value="true" />
        <add key="LogLevel" value="Info" />
        <add key="LogPath" value="Logs\server.log" />
        <add key="HeartbeatInterval" value="60000" />
        <add key="SessionTimeout" value="1800000" />
    </appSettings>
</configuration>
