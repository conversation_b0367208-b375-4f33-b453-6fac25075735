namespace HorizonLoader.Server.Forms
{
    partial class AdminForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabDashboard = new System.Windows.Forms.TabPage();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.lblActiveConnections = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.lblServerStatus = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.btnStopServer = new System.Windows.Forms.Button();
            this.btnStartServer = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.lblOnlineUsers = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.lblLockedUsers = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.lblActiveUsers = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.lblTotalUsers = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.lblExpiredSerialKeys = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.lblActiveSerialKeys = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.lblTotalSerialKeys = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.tabUsers = new System.Windows.Forms.TabPage();
            this.panel2 = new System.Windows.Forms.Panel();
            this.btnUnlockUser = new System.Windows.Forms.Button();
            this.btnResetHWID = new System.Windows.Forms.Button();
            this.dgvUserAccounts = new System.Windows.Forms.DataGridView();
            this.tabSerialKeys = new System.Windows.Forms.TabPage();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnGenerateSerialKey = new System.Windows.Forms.Button();
            this.dgvSerialKeys = new System.Windows.Forms.DataGridView();
            this.tabSettings = new System.Windows.Forms.TabPage();
            this.btnViewLogs = new System.Windows.Forms.Button();
            this.btnConfiguration = new System.Windows.Forms.Button();
            this.tabControl1.SuspendLayout();
            this.tabDashboard.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabUsers.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvUserAccounts)).BeginInit();
            this.tabSerialKeys.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvSerialKeys)).BeginInit();
            this.tabSettings.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabDashboard);
            this.tabControl1.Controls.Add(this.tabUsers);
            this.tabControl1.Controls.Add(this.tabSerialKeys);
            this.tabControl1.Controls.Add(this.tabSettings);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1000, 600);
            this.tabControl1.TabIndex = 0;
            // 
            // tabDashboard
            // 
            this.tabDashboard.Controls.Add(this.groupBox3);
            this.tabDashboard.Controls.Add(this.groupBox2);
            this.tabDashboard.Controls.Add(this.groupBox1);
            this.tabDashboard.Location = new System.Drawing.Point(4, 22);
            this.tabDashboard.Name = "tabDashboard";
            this.tabDashboard.Padding = new System.Windows.Forms.Padding(3);
            this.tabDashboard.Size = new System.Drawing.Size(992, 574);
            this.tabDashboard.TabIndex = 0;
            this.tabDashboard.Text = "Dashboard";
            this.tabDashboard.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.lblActiveConnections);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.lblServerStatus);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.btnStopServer);
            this.groupBox3.Controls.Add(this.btnStartServer);
            this.groupBox3.Location = new System.Drawing.Point(6, 200);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(980, 100);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "Server Status";
            // 
            // lblActiveConnections
            // 
            this.lblActiveConnections.AutoSize = true;
            this.lblActiveConnections.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold);
            this.lblActiveConnections.Location = new System.Drawing.Point(150, 50);
            this.lblActiveConnections.Name = "lblActiveConnections";
            this.lblActiveConnections.Size = new System.Drawing.Size(19, 20);
            this.lblActiveConnections.TabIndex = 5;
            this.lblActiveConnections.Text = "0";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(10, 53);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(104, 13);
            this.label12.TabIndex = 4;
            this.label12.Text = "Active Connections:";
            // 
            // lblServerStatus
            // 
            this.lblServerStatus.AutoSize = true;
            this.lblServerStatus.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold);
            this.lblServerStatus.ForeColor = System.Drawing.Color.Red;
            this.lblServerStatus.Location = new System.Drawing.Point(100, 25);
            this.lblServerStatus.Name = "lblServerStatus";
            this.lblServerStatus.Size = new System.Drawing.Size(73, 20);
            this.lblServerStatus.TabIndex = 3;
            this.lblServerStatus.Text = "Stopped";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(10, 28);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(75, 13);
            this.label10.TabIndex = 2;
            this.label10.Text = "Server Status:";
            // 
            // btnStopServer
            // 
            this.btnStopServer.Enabled = false;
            this.btnStopServer.Location = new System.Drawing.Point(400, 40);
            this.btnStopServer.Name = "btnStopServer";
            this.btnStopServer.Size = new System.Drawing.Size(100, 30);
            this.btnStopServer.TabIndex = 1;
            this.btnStopServer.Text = "Stop Server";
            this.btnStopServer.UseVisualStyleBackColor = true;
            this.btnStopServer.Click += new System.EventHandler(this.btnStopServer_Click);
            // 
            // btnStartServer
            // 
            this.btnStartServer.Location = new System.Drawing.Point(300, 40);
            this.btnStartServer.Name = "btnStartServer";
            this.btnStartServer.Size = new System.Drawing.Size(100, 30);
            this.btnStartServer.TabIndex = 0;
            this.btnStartServer.Text = "Start Server";
            this.btnStartServer.UseVisualStyleBackColor = true;
            this.btnStartServer.Click += new System.EventHandler(this.btnStartServer_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.lblOnlineUsers);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.lblLockedUsers);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.lblActiveUsers);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.lblTotalUsers);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Location = new System.Drawing.Point(500, 6);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(486, 188);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "User Statistics";
            // 
            // lblOnlineUsers
            // 
            this.lblOnlineUsers.AutoSize = true;
            this.lblOnlineUsers.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblOnlineUsers.ForeColor = System.Drawing.Color.Blue;
            this.lblOnlineUsers.Location = new System.Drawing.Point(150, 140);
            this.lblOnlineUsers.Name = "lblOnlineUsers";
            this.lblOnlineUsers.Size = new System.Drawing.Size(25, 26);
            this.lblOnlineUsers.TabIndex = 7;
            this.lblOnlineUsers.Text = "0";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(10, 148);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(73, 13);
            this.label8.TabIndex = 6;
            this.label8.Text = "Online Users:";
            // 
            // lblLockedUsers
            // 
            this.lblLockedUsers.AutoSize = true;
            this.lblLockedUsers.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblLockedUsers.ForeColor = System.Drawing.Color.Red;
            this.lblLockedUsers.Location = new System.Drawing.Point(150, 105);
            this.lblLockedUsers.Name = "lblLockedUsers";
            this.lblLockedUsers.Size = new System.Drawing.Size(25, 26);
            this.lblLockedUsers.TabIndex = 5;
            this.lblLockedUsers.Text = "0";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(10, 113);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(78, 13);
            this.label6.TabIndex = 4;
            this.label6.Text = "Locked Users:";
            // 
            // lblActiveUsers
            // 
            this.lblActiveUsers.AutoSize = true;
            this.lblActiveUsers.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblActiveUsers.ForeColor = System.Drawing.Color.Green;
            this.lblActiveUsers.Location = new System.Drawing.Point(150, 70);
            this.lblActiveUsers.Name = "lblActiveUsers";
            this.lblActiveUsers.Size = new System.Drawing.Size(25, 26);
            this.lblActiveUsers.TabIndex = 3;
            this.lblActiveUsers.Text = "0";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(10, 78);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(73, 13);
            this.label4.TabIndex = 2;
            this.label4.Text = "Active Users:";
            // 
            // lblTotalUsers
            // 
            this.lblTotalUsers.AutoSize = true;
            this.lblTotalUsers.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblTotalUsers.Location = new System.Drawing.Point(150, 35);
            this.lblTotalUsers.Name = "lblTotalUsers";
            this.lblTotalUsers.Size = new System.Drawing.Size(25, 26);
            this.lblTotalUsers.TabIndex = 1;
            this.lblTotalUsers.Text = "0";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(10, 43);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(67, 13);
            this.label2.TabIndex = 0;
            this.label2.Text = "Total Users:";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.lblExpiredSerialKeys);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.lblActiveSerialKeys);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.lblTotalSerialKeys);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Location = new System.Drawing.Point(6, 6);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(488, 188);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Serial Key Statistics";
            // 
            // lblExpiredSerialKeys
            // 
            this.lblExpiredSerialKeys.AutoSize = true;
            this.lblExpiredSerialKeys.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblExpiredSerialKeys.ForeColor = System.Drawing.Color.Red;
            this.lblExpiredSerialKeys.Location = new System.Drawing.Point(150, 105);
            this.lblExpiredSerialKeys.Name = "lblExpiredSerialKeys";
            this.lblExpiredSerialKeys.Size = new System.Drawing.Size(25, 26);
            this.lblExpiredSerialKeys.TabIndex = 5;
            this.lblExpiredSerialKeys.Text = "0";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(10, 113);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(103, 13);
            this.label7.TabIndex = 4;
            this.label7.Text = "Expired Serial Keys:";
            // 
            // lblActiveSerialKeys
            // 
            this.lblActiveSerialKeys.AutoSize = true;
            this.lblActiveSerialKeys.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblActiveSerialKeys.ForeColor = System.Drawing.Color.Green;
            this.lblActiveSerialKeys.Location = new System.Drawing.Point(150, 70);
            this.lblActiveSerialKeys.Name = "lblActiveSerialKeys";
            this.lblActiveSerialKeys.Size = new System.Drawing.Size(25, 26);
            this.lblActiveSerialKeys.TabIndex = 3;
            this.lblActiveSerialKeys.Text = "0";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(10, 78);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(97, 13);
            this.label5.TabIndex = 2;
            this.label5.Text = "Active Serial Keys:";
            // 
            // lblTotalSerialKeys
            // 
            this.lblTotalSerialKeys.AutoSize = true;
            this.lblTotalSerialKeys.Font = new System.Drawing.Font("Microsoft Sans Serif", 16F, System.Drawing.FontStyle.Bold);
            this.lblTotalSerialKeys.Location = new System.Drawing.Point(150, 35);
            this.lblTotalSerialKeys.Name = "lblTotalSerialKeys";
            this.lblTotalSerialKeys.Size = new System.Drawing.Size(25, 26);
            this.lblTotalSerialKeys.TabIndex = 1;
            this.lblTotalSerialKeys.Text = "0";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(10, 43);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(91, 13);
            this.label1.TabIndex = 0;
            this.label1.Text = "Total Serial Keys:";
            // 
            // tabUsers
            // 
            this.tabUsers.Controls.Add(this.panel2);
            this.tabUsers.Controls.Add(this.dgvUserAccounts);
            this.tabUsers.Location = new System.Drawing.Point(4, 22);
            this.tabUsers.Name = "tabUsers";
            this.tabUsers.Padding = new System.Windows.Forms.Padding(3);
            this.tabUsers.Size = new System.Drawing.Size(992, 574);
            this.tabUsers.TabIndex = 1;
            this.tabUsers.Text = "User Accounts";
            this.tabUsers.UseVisualStyleBackColor = true;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.btnUnlockUser);
            this.panel2.Controls.Add(this.btnResetHWID);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(3, 531);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(986, 40);
            this.panel2.TabIndex = 1;
            // 
            // btnUnlockUser
            // 
            this.btnUnlockUser.Location = new System.Drawing.Point(120, 5);
            this.btnUnlockUser.Name = "btnUnlockUser";
            this.btnUnlockUser.Size = new System.Drawing.Size(100, 30);
            this.btnUnlockUser.TabIndex = 1;
            this.btnUnlockUser.Text = "Unlock User";
            this.btnUnlockUser.UseVisualStyleBackColor = true;
            this.btnUnlockUser.Click += new System.EventHandler(this.btnUnlockUser_Click);
            // 
            // btnResetHWID
            // 
            this.btnResetHWID.Location = new System.Drawing.Point(10, 5);
            this.btnResetHWID.Name = "btnResetHWID";
            this.btnResetHWID.Size = new System.Drawing.Size(100, 30);
            this.btnResetHWID.TabIndex = 0;
            this.btnResetHWID.Text = "Reset HWID";
            this.btnResetHWID.UseVisualStyleBackColor = true;
            this.btnResetHWID.Click += new System.EventHandler(this.btnResetHWID_Click);
            // 
            // dgvUserAccounts
            // 
            this.dgvUserAccounts.AllowUserToAddRows = false;
            this.dgvUserAccounts.AllowUserToDeleteRows = false;
            this.dgvUserAccounts.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvUserAccounts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvUserAccounts.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvUserAccounts.Location = new System.Drawing.Point(3, 3);
            this.dgvUserAccounts.MultiSelect = false;
            this.dgvUserAccounts.Name = "dgvUserAccounts";
            this.dgvUserAccounts.ReadOnly = true;
            this.dgvUserAccounts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvUserAccounts.Size = new System.Drawing.Size(986, 568);
            this.dgvUserAccounts.TabIndex = 0;
            // 
            // tabSerialKeys
            // 
            this.tabSerialKeys.Controls.Add(this.panel1);
            this.tabSerialKeys.Controls.Add(this.dgvSerialKeys);
            this.tabSerialKeys.Location = new System.Drawing.Point(4, 22);
            this.tabSerialKeys.Name = "tabSerialKeys";
            this.tabSerialKeys.Size = new System.Drawing.Size(992, 574);
            this.tabSerialKeys.TabIndex = 2;
            this.tabSerialKeys.Text = "Serial Keys";
            this.tabSerialKeys.UseVisualStyleBackColor = true;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.btnGenerateSerialKey);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(0, 534);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(992, 40);
            this.panel1.TabIndex = 1;
            // 
            // btnGenerateSerialKey
            // 
            this.btnGenerateSerialKey.Location = new System.Drawing.Point(10, 5);
            this.btnGenerateSerialKey.Name = "btnGenerateSerialKey";
            this.btnGenerateSerialKey.Size = new System.Drawing.Size(120, 30);
            this.btnGenerateSerialKey.TabIndex = 0;
            this.btnGenerateSerialKey.Text = "Generate Serial Key";
            this.btnGenerateSerialKey.UseVisualStyleBackColor = true;
            this.btnGenerateSerialKey.Click += new System.EventHandler(this.btnGenerateSerialKey_Click);
            // 
            // dgvSerialKeys
            // 
            this.dgvSerialKeys.AllowUserToAddRows = false;
            this.dgvSerialKeys.AllowUserToDeleteRows = false;
            this.dgvSerialKeys.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvSerialKeys.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvSerialKeys.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvSerialKeys.Location = new System.Drawing.Point(0, 0);
            this.dgvSerialKeys.MultiSelect = false;
            this.dgvSerialKeys.Name = "dgvSerialKeys";
            this.dgvSerialKeys.ReadOnly = true;
            this.dgvSerialKeys.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvSerialKeys.Size = new System.Drawing.Size(992, 574);
            this.dgvSerialKeys.TabIndex = 0;
            // 
            // tabSettings
            // 
            this.tabSettings.Controls.Add(this.btnViewLogs);
            this.tabSettings.Controls.Add(this.btnConfiguration);
            this.tabSettings.Location = new System.Drawing.Point(4, 22);
            this.tabSettings.Name = "tabSettings";
            this.tabSettings.Size = new System.Drawing.Size(992, 574);
            this.tabSettings.TabIndex = 3;
            this.tabSettings.Text = "Settings";
            this.tabSettings.UseVisualStyleBackColor = true;
            // 
            // btnViewLogs
            // 
            this.btnViewLogs.Location = new System.Drawing.Point(20, 60);
            this.btnViewLogs.Name = "btnViewLogs";
            this.btnViewLogs.Size = new System.Drawing.Size(150, 40);
            this.btnViewLogs.TabIndex = 1;
            this.btnViewLogs.Text = "View Logs";
            this.btnViewLogs.UseVisualStyleBackColor = true;
            this.btnViewLogs.Click += new System.EventHandler(this.btnViewLogs_Click);
            // 
            // btnConfiguration
            // 
            this.btnConfiguration.Location = new System.Drawing.Point(20, 20);
            this.btnConfiguration.Name = "btnConfiguration";
            this.btnConfiguration.Size = new System.Drawing.Size(150, 40);
            this.btnConfiguration.TabIndex = 0;
            this.btnConfiguration.Text = "Configuration";
            this.btnConfiguration.UseVisualStyleBackColor = true;
            this.btnConfiguration.Click += new System.EventHandler(this.btnConfiguration_Click);
            // 
            // AdminForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Controls.Add(this.tabControl1);
            this.MinimumSize = new System.Drawing.Size(800, 600);
            this.Name = "AdminForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "HorizonLoader Server - Admin Panel";
            this.tabControl1.ResumeLayout(false);
            this.tabDashboard.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabUsers.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvUserAccounts)).EndInit();
            this.tabSerialKeys.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvSerialKeys)).EndInit();
            this.tabSettings.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabDashboard;
        private System.Windows.Forms.TabPage tabUsers;
        private System.Windows.Forms.TabPage tabSerialKeys;
        private System.Windows.Forms.TabPage tabSettings;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label lblTotalSerialKeys;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label lblExpiredSerialKeys;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label lblActiveSerialKeys;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label lblOnlineUsers;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label lblLockedUsers;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label lblActiveUsers;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label lblTotalUsers;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label lblActiveConnections;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label lblServerStatus;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Button btnStopServer;
        private System.Windows.Forms.Button btnStartServer;
        private System.Windows.Forms.DataGridView dgvUserAccounts;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button btnUnlockUser;
        private System.Windows.Forms.Button btnResetHWID;
        private System.Windows.Forms.DataGridView dgvSerialKeys;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button btnGenerateSerialKey;
        private System.Windows.Forms.Button btnViewLogs;
        private System.Windows.Forms.Button btnConfiguration;
    }
}
