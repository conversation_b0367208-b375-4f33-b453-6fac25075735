<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>
    <appSettings>
        <add key="ServerAddress" value="127.0.0.1" />
        <add key="ServerPort" value="8080" />
  <!-- Shared symmetric encryption key (Base64) -->
  <add key="EncryptionKey" value="AqJ4s9Mz7f6+eXgk+7dZ03sG3E1Dlzfz2zoJ3k0hqws=" />
        <add key="ConnectionTimeout" value="30000" />
        <add key="HeartbeatInterval" value="60000" />
        <add key="EnableLogging" value="true" />
        <add key="LogLevel" value="Info" />
    </appSettings>
</configuration>
