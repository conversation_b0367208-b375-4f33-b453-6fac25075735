using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using HorizonLoader.Shared.Models;
using HorizonLoader.Shared.Network;
using HorizonLoader.Shared.Security;
using HorizonLoader.Server.Services;

namespace HorizonLoader.Server.Network
{
    /// <summary>
    /// Manages TCP server connections and encrypted communication
    /// </summary>
    public class ServerNetworkManager : IDisposable
    {
        private readonly int _port;
        private readonly byte[] _encryptionKey;
        private readonly UserManagementService _userManagementService;
        private readonly SerialKeyService _serialKeyService;
        private readonly LoggingService _loggingService;
        
        private TcpListener _tcpListener;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly ConcurrentDictionary<string, ClientConnection> _activeConnections;
        private bool _isRunning;
        private readonly object _lockObject = new object();

        public event EventHandler<ClientConnectedEventArgs> ClientConnected;
        public event EventHandler<ClientDisconnectedEventArgs> ClientDisconnected;
        public event EventHandler<PacketReceivedEventArgs> PacketReceived;

        public bool IsRunning => _isRunning;
        public int ActiveConnectionCount => _activeConnections.Count;

        public ServerNetworkManager(int port, byte[] encryptionKey, UserManagementService userManagementService, 
            SerialKeyService serialKeyService, LoggingService loggingService)
        {
            _port = port;
            _encryptionKey = encryptionKey ?? throw new ArgumentNullException(nameof(encryptionKey));
            _userManagementService = userManagementService ?? throw new ArgumentNullException(nameof(userManagementService));
            _serialKeyService = serialKeyService ?? throw new ArgumentNullException(nameof(serialKeyService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            
            _activeConnections = new ConcurrentDictionary<string, ClientConnection>();
        }

        /// <summary>
        /// Starts the TCP server
        /// </summary>
        public async Task StartAsync()
        {
            lock (_lockObject)
            {
                if (_isRunning)
                    return;

                _isRunning = true;
            }

            try
            {
                _tcpListener = new TcpListener(IPAddress.Any, _port);
                _cancellationTokenSource = new CancellationTokenSource();
                
                _tcpListener.Start();
                _loggingService.LogInfo($"Server started on port {_port}");

                // Start accepting connections
                _ = Task.Run(AcceptConnectionsAsync, _cancellationTokenSource.Token);
                
                // Start heartbeat monitoring
                _ = Task.Run(MonitorHeartbeatsAsync, _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to start server: {ex.Message}", ex);
                _isRunning = false;
                throw;
            }
        }

        /// <summary>
        /// Stops the TCP server
        /// </summary>
        public async Task StopAsync()
        {
            lock (_lockObject)
            {
                if (!_isRunning)
                    return;

                _isRunning = false;
            }

            try
            {
                _cancellationTokenSource?.Cancel();
                _tcpListener?.Stop();

                // Disconnect all clients
                var disconnectTasks = new Task[_activeConnections.Count];
                int index = 0;
                foreach (var connection in _activeConnections.Values)
                {
                    disconnectTasks[index++] = DisconnectClientAsync(connection.ConnectionId);
                }

                if (disconnectTasks.Length > 0)
                {
                    await Task.WhenAll(disconnectTasks);
                }

                _loggingService.LogInfo("Server stopped");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error stopping server: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Accepts incoming client connections
        /// </summary>
        private async Task AcceptConnectionsAsync()
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var tcpClient = await _tcpListener.AcceptTcpClientAsync();
                    var clientEndpoint = tcpClient.Client.RemoteEndPoint as IPEndPoint;
                    string clientIp = clientEndpoint?.Address.ToString() ?? "Unknown";

                    _loggingService.LogNetworkEvent($"New connection from {clientIp}");

                    // Handle client in separate task
                    _ = Task.Run(() => HandleClientAsync(tcpClient, clientIp), _cancellationTokenSource.Token);
                }
                catch (ObjectDisposedException)
                {
                    // Server is shutting down
                    break;
                }
                catch (Exception ex)
                {
                    if (!_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        _loggingService.LogError($"Error accepting connection: {ex.Message}");
                        await Task.Delay(1000); // Brief delay before retrying
                    }
                }
            }
        }

        /// <summary>
        /// Handles communication with a connected client
        /// </summary>
        private async Task HandleClientAsync(TcpClient tcpClient, string clientIp)
        {
            string connectionId = Guid.NewGuid().ToString();
            ClientConnection connection = null;

            try
            {
                connection = new ClientConnection
                {
                    ConnectionId = connectionId,
                    TcpClient = tcpClient,
                    NetworkStream = tcpClient.GetStream(),
                    ClientIp = clientIp,
                    ConnectedTime = DateTime.UtcNow,
                    LastHeartbeat = DateTime.UtcNow
                };

                _activeConnections.TryAdd(connectionId, connection);
                ClientConnected?.Invoke(this, new ClientConnectedEventArgs { Connection = connection });

                // Handle client packets
                while (tcpClient.Connected && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    var packet = await NetworkProtocol.ReceivePacketAsync(connection.NetworkStream, _encryptionKey, 30000);
                    if (packet == null)
                        break;

                    connection.LastHeartbeat = DateTime.UtcNow;
                    await ProcessPacketAsync(connection, packet);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error handling client {clientIp}: {ex.Message}");
            }
            finally
            {
                if (connection != null)
                {
                    await DisconnectClientAsync(connectionId);
                }
            }
        }

        /// <summary>
        /// Processes received packets from clients
        /// </summary>
        private async Task ProcessPacketAsync(ClientConnection connection, NetworkPacket packet)
        {
            try
            {
                PacketReceived?.Invoke(this, new PacketReceivedEventArgs { Connection = connection, Packet = packet });

                NetworkPacket response = null;

                switch (packet.PacketType)
                {
                    case PacketType.AuthenticationRequest:
                        response = await HandleAuthenticationRequestAsync(connection, packet);
                        break;

                    case PacketType.HeartbeatRequest:
                        response = NetworkProtocol.CreateHeartbeatResponse(packet.SessionId);
                        break;

                    case PacketType.LicenseValidationRequest:
                        response = await HandleLicenseValidationRequestAsync(connection, packet);
                        break;

                    case PacketType.SessionEndRequest:
                        response = await HandleSessionEndRequestAsync(connection, packet);
                        break;

                    default:
                        response = NetworkProtocol.CreateErrorPacket("Unknown packet type", packet.SessionId);
                        break;
                }

                if (response != null)
                {
                    await SendPacketAsync(connection, response);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error processing packet from {connection.ClientIp}: {ex.Message}");
                var errorResponse = NetworkProtocol.CreateErrorPacket("Internal server error", packet.SessionId);
                await SendPacketAsync(connection, errorResponse);
            }
        }

        /// <summary>
        /// Handles authentication requests
        /// </summary>
        private async Task<NetworkPacket> HandleAuthenticationRequestAsync(ClientConnection connection, NetworkPacket packet)
        {
            var authRequest = packet.GetData<AuthenticationRequest>();
            if (authRequest == null)
            {
                return new NetworkPacket(PacketType.AuthenticationResponse, 
                    AuthenticationResponse.Failure(AuthenticationResult.ServerError, "Invalid authentication request"));
            }

            var authResponse = _userManagementService.AuthenticateUser(authRequest.SerialKey, authRequest.Hwid, connection.ClientIp);
            
            if (authResponse.Result == AuthenticationResult.Success)
            {
                connection.SessionId = authResponse.SessionId;
                connection.SerialKey = authRequest.SerialKey;
                connection.IsAuthenticated = true;
                _loggingService.LogAuthenticationEvent($"Client {connection.ClientIp} authenticated with serial key {authRequest.SerialKey}");
            }
            else
            {
                _loggingService.LogSecurityEvent($"Authentication failed for {connection.ClientIp}: {authResponse.Message}");
            }

            return new NetworkPacket(PacketType.AuthenticationResponse, authResponse);
        }

        /// <summary>
        /// Handles license validation requests
        /// </summary>
        private async Task<NetworkPacket> HandleLicenseValidationRequestAsync(ClientConnection connection, NetworkPacket packet)
        {
            if (!connection.IsAuthenticated)
            {
                return new NetworkPacket(PacketType.LicenseValidationResponse, 
                    LicenseValidationResponse.Invalid("Not authenticated"));
            }

            var serialKey = _serialKeyService.GetSerialKeyInfo(connection.SerialKey)?.SerialKey;
            if (serialKey == null)
            {
                return new NetworkPacket(PacketType.LicenseValidationResponse, 
                    LicenseValidationResponse.Invalid("Serial key not found"));
            }

            var response = serialKey.IsValid() 
                ? LicenseValidationResponse.Valid(serialKey.ExpirationDate)
                : LicenseValidationResponse.Invalid("License expired or invalid");

            return new NetworkPacket(PacketType.LicenseValidationResponse, response);
        }

        /// <summary>
        /// Handles session end requests
        /// </summary>
        private async Task<NetworkPacket> HandleSessionEndRequestAsync(ClientConnection connection, NetworkPacket packet)
        {
            if (connection.IsAuthenticated && !string.IsNullOrEmpty(connection.SerialKey))
            {
                _userManagementService.EndSession(connection.SerialKey);
                connection.IsAuthenticated = false;
                connection.SessionId = null;
            }

            var response = new { success = true, message = "Session ended" };
            return new NetworkPacket(PacketType.SessionEndResponse, response);
        }

        /// <summary>
        /// Sends a packet to a client
        /// </summary>
        private async Task<bool> SendPacketAsync(ClientConnection connection, NetworkPacket packet)
        {
            try
            {
                return await NetworkProtocol.SendPacketAsync(connection.NetworkStream, packet, _encryptionKey);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error sending packet to {connection.ClientIp}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects a client
        /// </summary>
        private async Task DisconnectClientAsync(string connectionId)
        {
            if (_activeConnections.TryRemove(connectionId, out ClientConnection connection))
            {
                try
                {
                    if (connection.IsAuthenticated && !string.IsNullOrEmpty(connection.SerialKey))
                    {
                        _userManagementService.EndSession(connection.SerialKey);
                    }

                    connection.NetworkStream?.Close();
                    connection.TcpClient?.Close();

                    _loggingService.LogNetworkEvent($"Client {connection.ClientIp} disconnected");
                    ClientDisconnected?.Invoke(this, new ClientDisconnectedEventArgs { Connection = connection });
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"Error disconnecting client {connection.ClientIp}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Monitors client heartbeats and disconnects inactive clients
        /// </summary>
        private async Task MonitorHeartbeatsAsync()
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var now = DateTime.UtcNow;
                    var timeoutThreshold = TimeSpan.FromMinutes(5); // 5 minute timeout

                    var connectionsToRemove = new List<string>();

                    foreach (var kvp in _activeConnections)
                    {
                        var connection = kvp.Value;
                        if (now - connection.LastHeartbeat > timeoutThreshold)
                        {
                            connectionsToRemove.Add(kvp.Key);
                        }
                    }

                    foreach (var connectionId in connectionsToRemove)
                    {
                        _loggingService.LogNetworkEvent($"Disconnecting inactive client: {connectionId}");
                        await DisconnectClientAsync(connectionId);
                    }

                    await Task.Delay(TimeSpan.FromMinutes(1), _cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"Error in heartbeat monitor: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            StopAsync().Wait();
            _cancellationTokenSource?.Dispose();
            _tcpListener = null;
        }
    }

    /// <summary>
    /// Represents a connected client
    /// </summary>
    public class ClientConnection
    {
        public string ConnectionId { get; set; }
        public TcpClient TcpClient { get; set; }
        public NetworkStream NetworkStream { get; set; }
        public string ClientIp { get; set; }
        public DateTime ConnectedTime { get; set; }
        public DateTime LastHeartbeat { get; set; }
        public bool IsAuthenticated { get; set; }
        public string SessionId { get; set; }
        public string SerialKey { get; set; }
    }

    /// <summary>
    /// Event arguments for client connection events
    /// </summary>
    public class ClientConnectedEventArgs : EventArgs
    {
        public ClientConnection Connection { get; set; }
    }

    public class ClientDisconnectedEventArgs : EventArgs
    {
        public ClientConnection Connection { get; set; }
    }

    public class PacketReceivedEventArgs : EventArgs
    {
        public ClientConnection Connection { get; set; }
        public NetworkPacket Packet { get; set; }
    }
}
