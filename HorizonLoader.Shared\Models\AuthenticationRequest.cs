using Newtonsoft.Json;

namespace HorizonLoader.Shared.Models
{
    /// <summary>
    /// Represents an authentication request from client to server
    /// </summary>
    public class AuthenticationRequest
    {
        [JsonProperty("serialKey")]
        public string SerialKey { get; set; }

        [JsonProperty("hwid")]
        public string Hwid { get; set; }

        [JsonProperty("clientVersion")]
        public string ClientVersion { get; set; }

        [JsonProperty("productId")]
        public string ProductId { get; set; }

        public AuthenticationRequest()
        {
        }

        public AuthenticationRequest(string serialKey, string hwid, string clientVersion = "1.0.0", string productId = null)
        {
            SerialKey = serialKey;
            Hwid = hwid;
            ClientVersion = clientVersion;
            ProductId = productId;
        }
    }

    /// <summary>
    /// Represents an admin authentication request
    /// </summary>
    public class AdminAuthenticationRequest
    {
        [JsonProperty("username")]
        public string Username { get; set; }

        [JsonProperty("password")]
        public string Password { get; set; }

        [JsonProperty("hwid")]
        public string Hwid { get; set; }

        public AdminAuthenticationRequest()
        {
        }

        public AdminAuthenticationRequest(string username, string password, string hwid)
        {
            Username = username;
            Password = password;
            Hwid = hwid;
        }
    }
}
