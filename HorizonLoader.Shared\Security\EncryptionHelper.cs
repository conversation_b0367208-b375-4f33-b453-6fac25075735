using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace HorizonLoader.Shared.Security
{
    /// <summary>
    /// Provides AES encryption and decryption functionality for secure communication
    /// </summary>
    public static class EncryptionHelper
    {
        private const int KEY_SIZE = 256; // AES-256
        private const int BLOCK_SIZE = 128;
        private const int IV_SIZE = 16; // 128 bits
        private const int SALT_SIZE = 32; // 256 bits
        private const int ITERATIONS = 10000; // PBKDF2 iterations

        /// <summary>
        /// Generates a cryptographically secure random key
        /// </summary>
        public static byte[] GenerateKey()
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                byte[] key = new byte[KEY_SIZE / 8];
                rng.GetBytes(key);
                return key;
            }
        }

        /// <summary>
        /// Derives a key from a password using PBKDF2
        /// </summary>
        public static byte[] DeriveKeyFromPassword(string password, byte[] salt = null)
        {
            if (salt == null)
            {
                salt = GenerateSalt();
            }

            using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, ITERATIONS))
            {
                return pbkdf2.GetBytes(KEY_SIZE / 8);
            }
        }

        /// <summary>
        /// Generates a cryptographically secure salt
        /// </summary>
        public static byte[] GenerateSalt()
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                byte[] salt = new byte[SALT_SIZE];
                rng.GetBytes(salt);
                return salt;
            }
        }

        /// <summary>
        /// Encrypts data using AES-256-CBC
        /// </summary>
        public static byte[] Encrypt(byte[] data, byte[] key)
        {
            if (data == null || data.Length == 0)
                throw new ArgumentException("Data cannot be null or empty");
            
            if (key == null || key.Length != KEY_SIZE / 8)
                throw new ArgumentException($"Key must be {KEY_SIZE / 8} bytes");

            try
            {
                using (var aes = new AesCryptoServiceProvider())
                {
                    aes.KeySize = KEY_SIZE;
                    aes.BlockSize = BLOCK_SIZE;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.Key = key;
                    aes.GenerateIV();

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    {
                        // Prepend IV to encrypted data
                        msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                        using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                        {
                            csEncrypt.Write(data, 0, data.Length);
                            csEncrypt.FlushFinalBlock();
                        }

                        return msEncrypt.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CryptographicException("Encryption failed", ex);
            }
        }

        /// <summary>
        /// Decrypts data using AES-256-CBC
        /// </summary>
        public static byte[] Decrypt(byte[] encryptedData, byte[] key)
        {
            if (encryptedData == null || encryptedData.Length <= IV_SIZE)
                return null;
            
            if (key == null || key.Length != KEY_SIZE / 8)
                return null;

            try
            {
                using (var aes = new AesCryptoServiceProvider())
                {
                    aes.KeySize = KEY_SIZE;
                    aes.BlockSize = BLOCK_SIZE;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.Key = key;

                    // Extract IV from the beginning of encrypted data
                    byte[] iv = new byte[IV_SIZE];
                    Array.Copy(encryptedData, 0, iv, 0, IV_SIZE);
                    aes.IV = iv;

                    // Extract actual encrypted data
                    byte[] cipherText = new byte[encryptedData.Length - IV_SIZE];
                    Array.Copy(encryptedData, IV_SIZE, cipherText, 0, cipherText.Length);

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(cipherText))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var msPlain = new MemoryStream())
                    {
                        csDecrypt.CopyTo(msPlain);
                        return msPlain.ToArray();
                    }
                }
            }
            catch (Exception)
            {
                return null; // Decryption failed
            }
        }

        /// <summary>
        /// Encrypts a string using AES-256-CBC
        /// </summary>
        public static string EncryptString(string plainText, byte[] key)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            byte[] data = Encoding.UTF8.GetBytes(plainText);
            byte[] encrypted = Encrypt(data, key);
            return Convert.ToBase64String(encrypted);
        }

        /// <summary>
        /// Decrypts a string using AES-256-CBC
        /// </summary>
        public static string DecryptString(string encryptedText, byte[] key)
        {
            if (string.IsNullOrEmpty(encryptedText))
                return string.Empty;

            try
            {
                byte[] encrypted = Convert.FromBase64String(encryptedText);
                byte[] decrypted = Decrypt(encrypted, key);
                
                if (decrypted == null)
                    return null;

                return Encoding.UTF8.GetString(decrypted);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Computes SHA-256 hash of data
        /// </summary>
        public static byte[] ComputeHash(byte[] data)
        {
            using (var sha256 = SHA256.Create())
            {
                return sha256.ComputeHash(data);
            }
        }

        /// <summary>
        /// Computes SHA-256 hash of a string
        /// </summary>
        public static string ComputeHashString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            byte[] data = Encoding.UTF8.GetBytes(input);
            byte[] hash = ComputeHash(data);
            return Convert.ToBase64String(hash);
        }

        /// <summary>
        /// Generates a secure random password
        /// </summary>
        public static string GenerateSecurePassword(int length = 32)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            using (var rng = new RNGCryptoServiceProvider())
            {
                byte[] randomBytes = new byte[length];
                rng.GetBytes(randomBytes);

                StringBuilder password = new StringBuilder(length);
                for (int i = 0; i < length; i++)
                {
                    password.Append(chars[randomBytes[i] % chars.Length]);
                }

                return password.ToString();
            }
        }

        /// <summary>
        /// Securely compares two byte arrays to prevent timing attacks
        /// </summary>
        public static bool SecureEquals(byte[] a, byte[] b)
        {
            if (a == null || b == null)
                return a == b;

            if (a.Length != b.Length)
                return false;

            int result = 0;
            for (int i = 0; i < a.Length; i++)
            {
                result |= a[i] ^ b[i];
            }

            return result == 0;
        }
    }
}
