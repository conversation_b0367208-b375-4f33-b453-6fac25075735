using System;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using HorizonLoader.Shared.Models;
using HorizonLoader.Shared.Network;
using HorizonLoader.Shared.Security;

namespace HorizonLoader.Client.Network
{
    /// <summary>
    /// Manages TCP client connections and encrypted communication with the server
    /// </summary>
    public class ClientNetworkManager : IDisposable
    {
        private readonly string _serverAddress;
        private readonly int _serverPort;
        private readonly byte[] _encryptionKey;
        private readonly int _connectionTimeout;
        private readonly int _heartbeatInterval;

        private TcpClient _tcpClient;
        private NetworkStream _networkStream;
        private CancellationTokenSource _cancellationTokenSource;
        private Timer _heartbeatTimer;
        private bool _isConnected;
        private string _sessionId;
        private readonly object _lockObject = new object();

        public event EventHandler<ConnectedEventArgs> Connected;
        public event EventHandler<DisconnectedEventArgs> Disconnected;
        public event EventHandler<PacketReceivedEventArgs> PacketReceived;
        public event EventHandler<ConnectionErrorEventArgs> ConnectionError;

        public bool IsConnected => _isConnected;
        public string SessionId => _sessionId;

        public ClientNetworkManager(string serverAddress, int serverPort, byte[] encryptionKey, 
            int connectionTimeout = 30000, int heartbeatInterval = 60000)
        {
            _serverAddress = serverAddress ?? throw new ArgumentNullException(nameof(serverAddress));
            _serverPort = serverPort;
            _encryptionKey = encryptionKey ?? throw new ArgumentNullException(nameof(encryptionKey));
            _connectionTimeout = connectionTimeout;
            _heartbeatInterval = heartbeatInterval;
        }

        /// <summary>
        /// Connects to the server
        /// </summary>
        public async Task<bool> ConnectAsync()
        {
            lock (_lockObject)
            {
                if (_isConnected)
                    return true;
            }

            try
            {
                _tcpClient = new TcpClient();
                _cancellationTokenSource = new CancellationTokenSource();

                // Set connection timeout
                var connectTask = _tcpClient.ConnectAsync(_serverAddress, _serverPort);
                var timeoutTask = Task.Delay(_connectionTimeout);

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                if (completedTask == timeoutTask)
                {
                    _tcpClient?.Close();
                    OnConnectionError("Connection timeout");
                    return false;
                }

                if (!_tcpClient.Connected)
                {
                    OnConnectionError("Failed to connect to server");
                    return false;
                }

                _networkStream = _tcpClient.GetStream();
                _isConnected = true;

                // Start receiving packets
                _ = Task.Run(ReceivePacketsAsync, _cancellationTokenSource.Token);

                // Start heartbeat timer
                _heartbeatTimer = new Timer(SendHeartbeat, null, TimeSpan.FromMilliseconds(_heartbeatInterval), 
                    TimeSpan.FromMilliseconds(_heartbeatInterval));

                OnConnected();
                return true;
            }
            catch (Exception ex)
            {
                OnConnectionError($"Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from the server
        /// </summary>
        public async Task DisconnectAsync()
        {
            lock (_lockObject)
            {
                if (!_isConnected)
                    return;

                _isConnected = false;
            }

            try
            {
                // Send session end request if we have a session
                if (!string.IsNullOrEmpty(_sessionId))
                {
                    var endSessionPacket = new NetworkPacket(PacketType.SessionEndRequest, null, _sessionId);
                    await SendPacketAsync(endSessionPacket);
                }

                _heartbeatTimer?.Dispose();
                _heartbeatTimer = null;

                _cancellationTokenSource?.Cancel();
                _networkStream?.Close();
                _tcpClient?.Close();

                _sessionId = null;
                OnDisconnected();
            }
            catch (Exception)
            {
                // Ignore errors during disconnect
            }
        }

        /// <summary>
        /// Authenticates with the server using serial key and HWID
        /// </summary>
        public async Task<AuthenticationResponse> AuthenticateAsync(string serialKey, string hwid, string clientVersion = "1.0.0")
        {
            if (!_isConnected)
                throw new InvalidOperationException("Not connected to server");

            try
            {
                var authRequest = new AuthenticationRequest(serialKey, hwid, clientVersion);
                var packet = new NetworkPacket(PacketType.AuthenticationRequest, authRequest);

                var response = await SendPacketAndWaitForResponseAsync(packet, PacketType.AuthenticationResponse);
                if (response != null)
                {
                    var authResponse = response.GetData<AuthenticationResponse>();
                    if (authResponse != null && authResponse.Result == AuthenticationResult.Success)
                    {
                        _sessionId = authResponse.SessionId;
                    }
                    return authResponse;
                }

                return AuthenticationResponse.Failure(AuthenticationResult.ServerError, "No response from server");
            }
            catch (Exception ex)
            {
                return AuthenticationResponse.Failure(AuthenticationResult.ServerError, $"Authentication error: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates the current license
        /// </summary>
        public async Task<LicenseValidationResponse> ValidateLicenseAsync()
        {
            if (!_isConnected || string.IsNullOrEmpty(_sessionId))
                throw new InvalidOperationException("Not authenticated");

            try
            {
                var packet = new NetworkPacket(PacketType.LicenseValidationRequest, null, _sessionId);
                var response = await SendPacketAndWaitForResponseAsync(packet, PacketType.LicenseValidationResponse);
                
                if (response != null)
                {
                    return response.GetData<LicenseValidationResponse>();
                }

                return LicenseValidationResponse.Invalid("No response from server");
            }
            catch (Exception ex)
            {
                return LicenseValidationResponse.Invalid($"License validation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Sends a packet to the server
        /// </summary>
        public async Task<bool> SendPacketAsync(NetworkPacket packet)
        {
            if (!_isConnected)
                return false;

            try
            {
                return await NetworkProtocol.SendPacketAsync(_networkStream, packet, _encryptionKey);
            }
            catch (Exception ex)
            {
                OnConnectionError($"Send error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sends a packet and waits for a specific response type
        /// </summary>
        private async Task<NetworkPacket> SendPacketAndWaitForResponseAsync(NetworkPacket packet, PacketType expectedResponseType, int timeoutMs = 10000)
        {
            var tcs = new TaskCompletionSource<NetworkPacket>();
            var timeoutCts = new CancellationTokenSource(timeoutMs);

            EventHandler<PacketReceivedEventArgs> handler = null;
            handler = (sender, e) =>
            {
                if (e.Packet.PacketType == expectedResponseType)
                {
                    PacketReceived -= handler;
                    tcs.TrySetResult(e.Packet);
                }
            };

            PacketReceived += handler;

            try
            {
                if (!await SendPacketAsync(packet))
                {
                    PacketReceived -= handler;
                    return null;
                }

                using (timeoutCts.Token.Register(() => tcs.TrySetCanceled()))
                {
                    return await tcs.Task;
                }
            }
            catch (OperationCanceledException)
            {
                PacketReceived -= handler;
                return null;
            }
            catch (Exception)
            {
                PacketReceived -= handler;
                return null;
            }
        }

        /// <summary>
        /// Continuously receives packets from the server
        /// </summary>
        private async Task ReceivePacketsAsync()
        {
            while (_isConnected && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var packet = await NetworkProtocol.ReceivePacketAsync(_networkStream, _encryptionKey, 30000);
                    if (packet == null)
                    {
                        OnConnectionError("Connection lost");
                        break;
                    }

                    OnPacketReceived(packet);
                }
                catch (Exception ex)
                {
                    if (_isConnected)
                    {
                        OnConnectionError($"Receive error: {ex.Message}");
                    }
                    break;
                }
            }

            // Connection lost, trigger disconnect
            if (_isConnected)
            {
                await DisconnectAsync();
            }
        }

        /// <summary>
        /// Sends a heartbeat to the server
        /// </summary>
        private async void SendHeartbeat(object state)
        {
            if (!_isConnected || string.IsNullOrEmpty(_sessionId))
                return;

            try
            {
                var heartbeatPacket = NetworkProtocol.CreateHeartbeatRequest(_sessionId);
                await SendPacketAsync(heartbeatPacket);
            }
            catch (Exception)
            {
                // Ignore heartbeat errors
            }
        }

        /// <summary>
        /// Handles connection established event
        /// </summary>
        private void OnConnected()
        {
            Connected?.Invoke(this, new ConnectedEventArgs { ServerAddress = _serverAddress, ServerPort = _serverPort });
        }

        /// <summary>
        /// Handles disconnection event
        /// </summary>
        private void OnDisconnected()
        {
            Disconnected?.Invoke(this, new DisconnectedEventArgs { Reason = "Disconnected" });
        }

        /// <summary>
        /// Handles packet received event
        /// </summary>
        private void OnPacketReceived(NetworkPacket packet)
        {
            PacketReceived?.Invoke(this, new PacketReceivedEventArgs { Packet = packet });
        }

        /// <summary>
        /// Handles connection error event
        /// </summary>
        private void OnConnectionError(string error)
        {
            ConnectionError?.Invoke(this, new ConnectionErrorEventArgs { Error = error });
        }

        public void Dispose()
        {
            DisconnectAsync().Wait();
            _cancellationTokenSource?.Dispose();
            _heartbeatTimer?.Dispose();
            _networkStream?.Dispose();
            _tcpClient?.Dispose();
        }
    }

    /// <summary>
    /// Event arguments for connection events
    /// </summary>
    public class ConnectedEventArgs : EventArgs
    {
        public string ServerAddress { get; set; }
        public int ServerPort { get; set; }
    }

    public class DisconnectedEventArgs : EventArgs
    {
        public string Reason { get; set; }
    }

    public class PacketReceivedEventArgs : EventArgs
    {
        public NetworkPacket Packet { get; set; }
    }

    public class ConnectionErrorEventArgs : EventArgs
    {
        public string Error { get; set; }
    }
}
