using System;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HorizonLoader.Shared.Models;
using HorizonLoader.Shared.Security;

namespace HorizonLoader.Shared.Network
{
    /// <summary>
    /// Handles network protocol operations for encrypted TCP communication
    /// </summary>
    public static class NetworkProtocol
    {
        private const int HEADER_SIZE = 8; // 4 bytes for packet length + 4 bytes for checksum
        private const int MAX_PACKET_SIZE = 1024 * 1024; // 1MB max packet size
        private const byte PROTOCOL_VERSION = 1;

        /// <summary>
        /// Sends an encrypted packet over the network
        /// </summary>
        public static async Task<bool> SendPacketAsync(NetworkStream stream, NetworkPacket packet, byte[] encryptionKey)
        {
            try
            {
                // Calculate checksum
                packet.CalculateChecksum();

                // Serialize packet to JSON
                string json = packet.ToJson();
                byte[] data = Encoding.UTF8.GetBytes(json);

                // Encrypt the data
                byte[] encryptedData = EncryptionHelper.Encrypt(data, encryptionKey);

                // Create header: [Protocol Version][Packet Length][Checksum]
                byte[] header = new byte[HEADER_SIZE];
                header[0] = PROTOCOL_VERSION;
                BitConverter.GetBytes(encryptedData.Length).CopyTo(header, 1);
                
                // Calculate header checksum
                uint headerChecksum = CalculateChecksum(encryptedData);
                BitConverter.GetBytes(headerChecksum).CopyTo(header, 5);

                // Send header first
                await stream.WriteAsync(header, 0, header.Length);

                // Send encrypted data
                await stream.WriteAsync(encryptedData, 0, encryptedData.Length);
                await stream.FlushAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Receives and decrypts a packet from the network
        /// </summary>
        public static async Task<NetworkPacket> ReceivePacketAsync(NetworkStream stream, byte[] encryptionKey, int timeoutMs = 30000)
        {
            try
            {
                // Set read timeout
                stream.ReadTimeout = timeoutMs;

                // Read header
                byte[] header = new byte[HEADER_SIZE];
                int bytesRead = await ReadExactAsync(stream, header, HEADER_SIZE);
                if (bytesRead != HEADER_SIZE)
                    return null;

                // Validate protocol version
                if (header[0] != PROTOCOL_VERSION)
                    return null;

                // Extract packet length
                int packetLength = BitConverter.ToInt32(header, 1);
                if (packetLength <= 0 || packetLength > MAX_PACKET_SIZE)
                    return null;

                // Extract header checksum
                uint expectedChecksum = BitConverter.ToUInt32(header, 5);

                // Read encrypted data
                byte[] encryptedData = new byte[packetLength];
                bytesRead = await ReadExactAsync(stream, encryptedData, packetLength);
                if (bytesRead != packetLength)
                    return null;

                // Validate checksum
                uint actualChecksum = CalculateChecksum(encryptedData);
                if (actualChecksum != expectedChecksum)
                    return null;

                // Decrypt data
                byte[] decryptedData = EncryptionHelper.Decrypt(encryptedData, encryptionKey);
                if (decryptedData == null)
                    return null;

                // Deserialize packet
                string json = Encoding.UTF8.GetString(decryptedData);
                NetworkPacket packet = NetworkPacket.FromJson(json);

                // Validate packet checksum
                if (packet != null && !packet.ValidateChecksum())
                    return null;

                return packet;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Reads exactly the specified number of bytes from the stream
        /// </summary>
        private static async Task<int> ReadExactAsync(NetworkStream stream, byte[] buffer, int count)
        {
            int totalBytesRead = 0;
            while (totalBytesRead < count)
            {
                int bytesRead = await stream.ReadAsync(buffer, totalBytesRead, count - totalBytesRead);
                if (bytesRead == 0)
                    break; // Connection closed

                totalBytesRead += bytesRead;
            }
            return totalBytesRead;
        }

        /// <summary>
        /// Calculates a simple checksum for data integrity
        /// </summary>
        private static uint CalculateChecksum(byte[] data)
        {
            uint checksum = 0;
            for (int i = 0; i < data.Length; i++)
            {
                checksum = (checksum << 1) ^ data[i];
            }
            return checksum;
        }

        /// <summary>
        /// Creates an error response packet
        /// </summary>
        public static NetworkPacket CreateErrorPacket(string errorMessage, string sessionId = null)
        {
            var errorData = new { error = errorMessage, timestamp = DateTime.UtcNow };
            return new NetworkPacket(PacketType.ErrorResponse, errorData, sessionId);
        }

        /// <summary>
        /// Creates a heartbeat request packet
        /// </summary>
        public static NetworkPacket CreateHeartbeatRequest(string sessionId)
        {
            var heartbeatData = new { timestamp = DateTime.UtcNow };
            return new NetworkPacket(PacketType.HeartbeatRequest, heartbeatData, sessionId);
        }

        /// <summary>
        /// Creates a heartbeat response packet
        /// </summary>
        public static NetworkPacket CreateHeartbeatResponse(string sessionId)
        {
            var heartbeatData = new { timestamp = DateTime.UtcNow, status = "alive" };
            return new NetworkPacket(PacketType.HeartbeatResponse, heartbeatData, sessionId);
        }

        /// <summary>
        /// Validates if a packet is a valid heartbeat
        /// </summary>
        public static bool IsValidHeartbeat(NetworkPacket packet)
        {
            return packet != null && 
                   (packet.PacketType == PacketType.HeartbeatRequest || 
                    packet.PacketType == PacketType.HeartbeatResponse) &&
                   !string.IsNullOrEmpty(packet.SessionId);
        }

        /// <summary>
        /// Checks if the packet timestamp is within acceptable range
        /// </summary>
        public static bool IsPacketTimestampValid(NetworkPacket packet, TimeSpan maxAge)
        {
            if (packet == null)
                return false;

            var age = DateTime.UtcNow - packet.Timestamp;
            return age <= maxAge && age >= TimeSpan.Zero;
        }
    }
}
