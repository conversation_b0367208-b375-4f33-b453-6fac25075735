using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using HorizonLoader.Client.Services;

namespace HorizonLoader.Client.Forms
{
    /// <summary>
    /// Main application form for the client
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly AuthenticationService _authenticationService;
        private readonly LicenseService _licenseService;
        private Timer _statusUpdateTimer;

        public MainForm(AuthenticationService authenticationService, LicenseService licenseService)
        {
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _licenseService = licenseService ?? throw new ArgumentNullException(nameof(licenseService));

            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Subscribe to service events
            _authenticationService.AuthenticationStateChanged += AuthenticationService_AuthenticationStateChanged;
            _licenseService.LicenseValidated += LicenseService_LicenseValidated;
            _licenseService.LicenseExpired += LicenseService_LicenseExpired;
            _licenseService.LicenseWarning += LicenseService_LicenseWarning;

            // Start license validation
            _licenseService.StartValidation();

            // Initialize status update timer
            _statusUpdateTimer = new Timer();
            _statusUpdateTimer.Interval = 1000; // Update every second
            _statusUpdateTimer.Tick += StatusUpdateTimer_Tick;
            _statusUpdateTimer.Start();

            // Initial status update
            UpdateStatus();
        }

        private void StatusUpdateTimer_Tick(object sender, EventArgs e)
        {
            UpdateStatus();
        }

        private void UpdateStatus()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(UpdateStatus));
                    return;
                }

                // Update authentication status
                if (_authenticationService.IsAuthenticated)
                {
                    lblAuthStatus.Text = "Authenticated";
                    lblAuthStatus.ForeColor = Color.Green;
                    lblSerialKey.Text = _authenticationService.CurrentSerialKey ?? "N/A";
                    lblHardwareId.Text = _authenticationService.CurrentHwid?.Substring(0, Math.Min(16, _authenticationService.CurrentHwid.Length)) + "...";
                }
                else
                {
                    lblAuthStatus.Text = "Not Authenticated";
                    lblAuthStatus.ForeColor = Color.Red;
                    lblSerialKey.Text = "N/A";
                    lblHardwareId.Text = "N/A";
                }

                // Update license status
                var licenseStatus = _licenseService.GetLicenseStatus();
                var statusMessage = _licenseService.GetLicenseStatusMessage();
                
                lblLicenseStatus.Text = statusMessage;
                
                switch (licenseStatus)
                {
                    case LicenseStatus.Valid:
                        lblLicenseStatus.ForeColor = Color.Green;
                        break;
                    case LicenseStatus.ExpiringThisMonth:
                        lblLicenseStatus.ForeColor = Color.Orange;
                        break;
                    case LicenseStatus.ExpiringSoon:
                    case LicenseStatus.Expired:
                    case LicenseStatus.Invalid:
                        lblLicenseStatus.ForeColor = Color.Red;
                        break;
                    default:
                        lblLicenseStatus.ForeColor = Color.Gray;
                        break;
                }

                // Update last validation time
                if (_licenseService.LastValidationTime.HasValue)
                {
                    lblLastValidation.Text = _licenseService.LastValidationTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                }
                else
                {
                    lblLastValidation.Text = "Never";
                }

                // Update days until expiration
                var daysRemaining = _licenseService.GetDaysUntilExpiration();
                if (daysRemaining >= 0)
                {
                    lblDaysRemaining.Text = daysRemaining.ToString();
                    lblDaysRemaining.ForeColor = daysRemaining <= 7 ? Color.Red : daysRemaining <= 30 ? Color.Orange : Color.Green;
                }
                else
                {
                    lblDaysRemaining.Text = "N/A";
                    lblDaysRemaining.ForeColor = Color.Gray;
                }

                // Update current time
                lblCurrentTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            catch (Exception ex)
            {
                // Log error but don't crash the application
                Console.WriteLine($"Error updating status: {ex.Message}");
            }
        }

        private void AuthenticationService_AuthenticationStateChanged(object sender, AuthenticationEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => AuthenticationService_AuthenticationStateChanged(sender, e)));
                return;
            }

            // Add to activity log
            AddToActivityLog($"Authentication: {e.Message}");

            if (!e.IsAuthenticated)
            {
                // Authentication lost, show login form
                ShowLoginForm();
            }
        }

        private void LicenseService_LicenseValidated(object sender, LicenseValidationEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => LicenseService_LicenseValidated(sender, e)));
                return;
            }

            string message = e.IsValid ? "License validated successfully" : $"License validation failed: {e.Response.Message}";
            AddToActivityLog($"License: {message}");
        }

        private void LicenseService_LicenseExpired(object sender, LicenseExpiredEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => LicenseService_LicenseExpired(sender, e)));
                return;
            }

            AddToActivityLog($"License expired: {e.Reason}");
            
            MessageBox.Show($"Your license has expired: {e.Reason}\n\nPlease contact support to renew your license.", 
                "License Expired", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private void LicenseService_LicenseWarning(object sender, LicenseWarningEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => LicenseService_LicenseWarning(sender, e)));
                return;
            }

            AddToActivityLog($"License warning: {e.Message}");

            if (e.Level == LicenseWarningLevel.Critical)
            {
                MessageBox.Show(e.Message, "License Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void AddToActivityLog(string message)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => AddToActivityLog(message)));
                    return;
                }

                string logEntry = $"[{DateTime.Now:HH:mm:ss}] {message}";
                
                txtActivityLog.AppendText(logEntry + Environment.NewLine);
                txtActivityLog.SelectionStart = txtActivityLog.Text.Length;
                txtActivityLog.ScrollToCaret();

                // Keep only last 100 lines
                var lines = txtActivityLog.Lines;
                if (lines.Length > 100)
                {
                    var recentLines = new string[100];
                    Array.Copy(lines, lines.Length - 100, recentLines, 0, 100);
                    txtActivityLog.Lines = recentLines;
                }
            }
            catch (Exception)
            {
                // Ignore logging errors
            }
        }

        private async void btnValidateLicense_Click(object sender, EventArgs e)
        {
            try
            {
                btnValidateLicense.Enabled = false;
                btnValidateLicense.Text = "Validating...";

                var result = await _licenseService.ValidateLicenseAsync();
                
                string message = result.IsValid ? "License is valid!" : $"License validation failed: {result.Message}";
                MessageBoxIcon icon = result.IsValid ? MessageBoxIcon.Information : MessageBoxIcon.Warning;
                
                MessageBox.Show(message, "License Validation", MessageBoxButtons.OK, icon);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error validating license: {ex.Message}", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnValidateLicense.Enabled = true;
                btnValidateLicense.Text = "Validate License";
            }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to logout?", "Confirm Logout", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                PerformLogout();
            }
        }

        private async void PerformLogout()
        {
            try
            {
                await _authenticationService.LogoutAsync();
                ShowLoginForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during logout: {ex.Message}", "Logout Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowLoginForm()
        {
            this.Hide();
            
            using (var loginForm = new LoginForm(_authenticationService))
            {
                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    this.Show();
                    AddToActivityLog("User logged in successfully");
                }
                else
                {
                    this.Close();
                }
            }
        }

        private void btnClearLog_Click(object sender, EventArgs e)
        {
            txtActivityLog.Clear();
            AddToActivityLog("Activity log cleared");
        }

        private void btnAbout_Click(object sender, EventArgs e)
        {
            string aboutText = "Horizon Loader Client\n" +
                              "Version 1.0.0\n\n" +
                              "A secure client-server application with\n" +
                              "hardware-based license protection.\n\n" +
                              "© 2025 Horizon Loader";

            MessageBox.Show(aboutText, "About Horizon Loader", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // Stop services
                _statusUpdateTimer?.Stop();
                _statusUpdateTimer?.Dispose();
                _licenseService?.StopValidation();

                // Unsubscribe from events
                _authenticationService.AuthenticationStateChanged -= AuthenticationService_AuthenticationStateChanged;
                _licenseService.LicenseValidated -= LicenseService_LicenseValidated;
                _licenseService.LicenseExpired -= LicenseService_LicenseExpired;
                _licenseService.LicenseWarning -= LicenseService_LicenseWarning;

                // Logout
                if (_authenticationService.IsAuthenticated)
                {
                    _authenticationService.LogoutAsync().Wait(5000); // Wait up to 5 seconds
                }
            }
            catch (Exception)
            {
                // Ignore cleanup errors
            }

            base.OnFormClosing(e);
        }
    }
}
