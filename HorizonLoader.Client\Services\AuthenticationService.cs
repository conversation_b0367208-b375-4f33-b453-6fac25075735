using System;
using System.Threading.Tasks;
using HorizonLoader.Shared.Models;
using HorizonLoader.Shared.Security;
using HorizonLoader.Client.Network;

namespace HorizonLoader.Client.Services
{
    /// <summary>
    /// Provides authentication services for the client application
    /// </summary>
    public class AuthenticationService
    {
        private readonly ClientNetworkManager _networkManager;
        private string _currentSerialKey;
        private string _currentHwid;
        private bool _isAuthenticated;
        private DateTime? _lastAuthenticationTime;
        private AuthenticationResponse _lastAuthenticationResponse;

        public event EventHandler<AuthenticationEventArgs> AuthenticationStateChanged;

        public bool IsAuthenticated => _isAuthenticated;
        public string CurrentSerialKey => _currentSerialKey;
        public string CurrentHwid => _currentHwid;
        public DateTime? LastAuthenticationTime => _lastAuthenticationTime;
        public AuthenticationResponse LastAuthenticationResponse => _lastAuthenticationResponse;

        public AuthenticationService(ClientNetworkManager networkManager)
        {
            _networkManager = networkManager ?? throw new ArgumentNullException(nameof(networkManager));
            
            // Subscribe to network events
            _networkManager.Connected += NetworkManager_Connected;
            _networkManager.Disconnected += NetworkManager_Disconnected;
            _networkManager.ConnectionError += NetworkManager_ConnectionError;
        }

        /// <summary>
        /// Authenticates with the server using a serial key
        /// </summary>
        public async Task<AuthenticationResult> AuthenticateAsync(string serialKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(serialKey))
                    return new AuthenticationResult { Success = false, ErrorMessage = "Serial key is required" };

                // Generate hardware ID
                string hwid = HardwareFingerprint.GenerateHwid();

                // Ensure we're connected to the server
                if (!_networkManager.IsConnected)
                {
                    bool connected = await _networkManager.ConnectAsync();
                    if (!connected)
                    {
                        return new AuthenticationResult { Success = false, ErrorMessage = "Failed to connect to server" };
                    }
                }

                // Send authentication request
                var response = await _networkManager.AuthenticateAsync(serialKey, hwid);
                
                if (response.Result == Shared.Models.AuthenticationResult.Success)
                {
                    _currentSerialKey = serialKey;
                    _currentHwid = hwid;
                    _isAuthenticated = true;
                    _lastAuthenticationTime = DateTime.UtcNow;
                    _lastAuthenticationResponse = response;

                    // Inform SecurityEventManager about authentication
                    SecurityEventManager.SetAuthenticationInfo(serialKey, hwid);

                    OnAuthenticationStateChanged(true, "Authentication successful");
                    
                    return new AuthenticationResult 
                    { 
                        Success = true, 
                        Message = "Authentication successful",
                        ExpirationDate = response.ExpirationDate,
                        ProductAccess = response.ProductAccess
                    };
                }
                else
                {
                    _isAuthenticated = false;
                    _lastAuthenticationResponse = response;
                    
                    OnAuthenticationStateChanged(false, response.Message);
                    
                    return new AuthenticationResult 
                    { 
                        Success = false, 
                        ErrorMessage = response.Message,
                        Result = response.Result,
                        CanResetHwid = response.CanResetHwid,
                        RemainingHwidResets = response.RemainingHwidResets,
                        LockoutEndTime = response.LockoutEndTime
                    };
                }
            }
            catch (Exception ex)
            {
                _isAuthenticated = false;
                OnAuthenticationStateChanged(false, $"Authentication error: {ex.Message}");
                
                return new AuthenticationResult 
                { 
                    Success = false, 
                    ErrorMessage = $"Authentication error: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// Validates the current authentication status
        /// </summary>
        public async Task<bool> ValidateAuthenticationAsync()
        {
            if (!_isAuthenticated || !_networkManager.IsConnected)
                return false;

            try
            {
                var licenseResponse = await _networkManager.ValidateLicenseAsync();
                if (licenseResponse != null && licenseResponse.IsValid)
                {
                    return true;
                }
                else
                {
                    _isAuthenticated = false;
                    OnAuthenticationStateChanged(false, licenseResponse?.Message ?? "License validation failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isAuthenticated = false;
                OnAuthenticationStateChanged(false, $"Validation error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Logs out the current user
        /// </summary>
        public async Task LogoutAsync()
        {
            try
            {
                if (_networkManager.IsConnected)
                {
                    await _networkManager.DisconnectAsync();
                }
            }
            catch (Exception)
            {
                // Ignore logout errors
            }
            finally
            {
                _isAuthenticated = false;
                _currentSerialKey = null;
                _currentHwid = null;
                _lastAuthenticationTime = null;
                _lastAuthenticationResponse = null;
                
                OnAuthenticationStateChanged(false, "Logged out");
            }
        }

        /// <summary>
        /// Gets the current hardware ID
        /// </summary>
        public string GetCurrentHardwareId()
        {
            return HardwareFingerprint.GenerateHwid();
        }

        /// <summary>
        /// Gets detailed hardware information for debugging
        /// </summary>
        public string GetHardwareInfo()
        {
            return HardwareFingerprint.GetHardwareInfo();
        }

        /// <summary>
        /// Checks if the license is about to expire
        /// </summary>
        public bool IsLicenseExpiringSoon(int daysThreshold = 7)
        {
            if (_lastAuthenticationResponse?.ExpirationDate == null)
                return false;

            var daysUntilExpiration = (_lastAuthenticationResponse.ExpirationDate.Value - DateTime.UtcNow).TotalDays;
            return daysUntilExpiration <= daysThreshold && daysUntilExpiration > 0;
        }

        /// <summary>
        /// Gets the number of days until license expiration
        /// </summary>
        public int GetDaysUntilExpiration()
        {
            if (_lastAuthenticationResponse?.ExpirationDate == null)
                return -1;

            var days = (int)(_lastAuthenticationResponse.ExpirationDate.Value - DateTime.UtcNow).TotalDays;
            return Math.Max(0, days);
        }

        /// <summary>
        /// Handles network connection established
        /// </summary>
        private void NetworkManager_Connected(object sender, ConnectedEventArgs e)
        {
            // Connection established, but not authenticated yet
        }

        /// <summary>
        /// Handles network disconnection
        /// </summary>
        private void NetworkManager_Disconnected(object sender, DisconnectedEventArgs e)
        {
            if (_isAuthenticated)
            {
                _isAuthenticated = false;
                OnAuthenticationStateChanged(false, $"Disconnected: {e.Reason}");
            }
        }

        /// <summary>
        /// Handles network connection errors
        /// </summary>
        private void NetworkManager_ConnectionError(object sender, ConnectionErrorEventArgs e)
        {
            if (_isAuthenticated)
            {
                _isAuthenticated = false;
                OnAuthenticationStateChanged(false, $"Connection error: {e.Error}");
            }
        }

        /// <summary>
        /// Raises the authentication state changed event
        /// </summary>
        private void OnAuthenticationStateChanged(bool isAuthenticated, string message)
        {
            AuthenticationStateChanged?.Invoke(this, new AuthenticationEventArgs
            {
                IsAuthenticated = isAuthenticated,
                Message = message,
                Timestamp = DateTime.UtcNow
            });
        }

        public void Dispose()
        {
            _networkManager.Connected -= NetworkManager_Connected;
            _networkManager.Disconnected -= NetworkManager_Disconnected;
            _networkManager.ConnectionError -= NetworkManager_ConnectionError;
        }
    }

    /// <summary>
    /// Result of an authentication attempt
    /// </summary>
    public class AuthenticationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string ErrorMessage { get; set; }
        public Shared.Models.AuthenticationResult Result { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public System.Collections.Generic.List<string> ProductAccess { get; set; }
        public bool CanResetHwid { get; set; }
        public int RemainingHwidResets { get; set; }
        public DateTime? LockoutEndTime { get; set; }
    }

    /// <summary>
    /// Event arguments for authentication state changes
    /// </summary>
    public class AuthenticationEventArgs : EventArgs
    {
        public bool IsAuthenticated { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
