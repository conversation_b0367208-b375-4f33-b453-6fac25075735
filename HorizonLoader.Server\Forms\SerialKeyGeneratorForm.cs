using System;
using System.Windows.Forms;
using HorizonLoader.Server.Services;

namespace HorizonLoader.Server.Forms
{
    /// <summary>
    /// Form for generating new serial keys
    /// </summary>
    public partial class SerialKeyGeneratorForm : Form
    {
        private readonly SerialKeyService _serialKeyService;
        private readonly LoggingService _loggingService;

        public SerialKeyGeneratorForm(SerialKeyService serialKeyService, LoggingService loggingService)
        {
            _serialKeyService = serialKeyService ?? throw new ArgumentNullException(nameof(serialKeyService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));

            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Set default values
            dtpExpirationDate.Value = DateTime.Now.AddMonths(1);
            nudMaxHwidResets.Value = 3;
            txtProductId.Text = "HORIZON_LOADER";
            txtProductName.Text = "Horizon Loader";
        }

        private void btnGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtProductId.Text))
                {
                    MessageBox.Show("Product ID is required.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtProductId.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtProductName.Text))
                {
                    MessageBox.Show("Product Name is required.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtProductName.Focus();
                    return;
                }

                if (dtpExpirationDate.Value <= DateTime.Now)
                {
                    MessageBox.Show("Expiration date must be in the future.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    dtpExpirationDate.Focus();
                    return;
                }

                // Generate serial key
                var serialKey = _serialKeyService.GenerateSerialKey(
                    txtProductId.Text.Trim(),
                    txtProductName.Text.Trim(),
                    dtpExpirationDate.Value,
                    "Admin",
                    (int)nudMaxHwidResets.Value);

                // Display generated key
                txtGeneratedKey.Text = serialKey.Key;
                lblKeyId.Text = serialKey.Id;
                lblCreatedDate.Text = serialKey.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss UTC");

                // Enable copy button
                btnCopyKey.Enabled = true;

                _loggingService.LogInfo($"Serial key generated: {serialKey.Key} for product {serialKey.ProductId}");

                MessageBox.Show("Serial key generated successfully!", "Success", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error generating serial key", ex);
                MessageBox.Show($"Error generating serial key: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCopyKey_Click(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(txtGeneratedKey.Text))
                {
                    Clipboard.SetText(txtGeneratedKey.Text);
                    MessageBox.Show("Serial key copied to clipboard!", "Copied", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error copying serial key to clipboard", ex);
                MessageBox.Show($"Error copying to clipboard: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtProductId_TextChanged(object sender, EventArgs e)
        {
            // Clear generated key when input changes
            ClearGeneratedKey();
        }

        private void txtProductName_TextChanged(object sender, EventArgs e)
        {
            // Clear generated key when input changes
            ClearGeneratedKey();
        }

        private void dtpExpirationDate_ValueChanged(object sender, EventArgs e)
        {
            // Clear generated key when input changes
            ClearGeneratedKey();
        }

        private void nudMaxHwidResets_ValueChanged(object sender, EventArgs e)
        {
            // Clear generated key when input changes
            ClearGeneratedKey();
        }

        private void ClearGeneratedKey()
        {
            txtGeneratedKey.Clear();
            lblKeyId.Text = "";
            lblCreatedDate.Text = "";
            btnCopyKey.Enabled = false;
        }
    }
}
