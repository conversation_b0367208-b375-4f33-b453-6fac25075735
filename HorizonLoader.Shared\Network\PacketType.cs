namespace HorizonLoader.Shared.Network
{
    /// <summary>
    /// Defines the types of network packets used in client-server communication
    /// </summary>
    public enum PacketType : byte
    {
        // Authentication packets
        AuthenticationRequest = 0x01,
        AuthenticationResponse = 0x02,
        
        // License validation packets
        LicenseValidationRequest = 0x03,
        LicenseValidationResponse = 0x04,
        
        // Heartbeat packets
        HeartbeatRequest = 0x05,
        HeartbeatResponse = 0x06,
        
        // Session management packets
        SessionStartRequest = 0x07,
        SessionStartResponse = 0x08,
        SessionEndRequest = 0x09,
        SessionEndResponse = 0x0A,
        
        // Admin packets
        AdminLoginRequest = 0x10,
        AdminLoginResponse = 0x11,
        CreateSerialKeyRequest = 0x12,
        CreateSerialKeyResponse = 0x13,
        ResetHwidRequest = 0x14,
        ResetHwidResponse = 0x15,
        GetUserListRequest = 0x16,
        GetUserListResponse = 0x17,
        UpdateUserRequest = 0x18,
        UpdateUserResponse = 0x19,
        GetConfigurationRequest = 0x1A,
        GetConfigurationResponse = 0x1B,
        UpdateConfigurationRequest = 0x1C,
        UpdateConfigurationResponse = 0x1D,
        
        // Error and status packets
        ErrorResponse = 0xFE,
        UnknownPacket = 0xFF
    }
}
