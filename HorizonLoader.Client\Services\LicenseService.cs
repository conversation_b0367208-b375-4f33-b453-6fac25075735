using System;
using System.Threading;
using System.Threading.Tasks;
using HorizonLoader.Shared.Models;
using HorizonLoader.Client.Network;

namespace HorizonLoader.Client.Services
{
    /// <summary>
    /// Provides license validation and monitoring services for the client
    /// </summary>
    public class LicenseService : IDisposable
    {
        private readonly ClientNetworkManager _networkManager;
        private readonly AuthenticationService _authenticationService;
        private Timer _validationTimer;
        private readonly int _validationInterval;
        private bool _isValidationEnabled;
        private LicenseValidationResponse _lastValidationResponse;
        private DateTime? _lastValidationTime;

        public event EventHandler<LicenseValidationEventArgs> LicenseValidated;
        public event EventHandler<LicenseExpiredEventArgs> LicenseExpired;
        public event EventHandler<LicenseWarningEventArgs> LicenseWarning;

        public bool IsValidationEnabled => _isValidationEnabled;
        public LicenseValidationResponse LastValidationResponse => _lastValidationResponse;
        public DateTime? LastValidationTime => _lastValidationTime;

        public LicenseService(ClientNetworkManager networkManager, AuthenticationService authenticationService, int validationIntervalMinutes = 5)
        {
            _networkManager = networkManager ?? throw new ArgumentNullException(nameof(networkManager));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _validationInterval = validationIntervalMinutes * 60 * 1000; // Convert to milliseconds

            // Subscribe to authentication events
            _authenticationService.AuthenticationStateChanged += AuthenticationService_AuthenticationStateChanged;
        }

        /// <summary>
        /// Starts automatic license validation
        /// </summary>
        public void StartValidation()
        {
            if (_isValidationEnabled)
                return;

            _isValidationEnabled = true;
            _validationTimer = new Timer(ValidateLicenseCallback, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(_validationInterval));
        }

        /// <summary>
        /// Stops automatic license validation
        /// </summary>
        public void StopValidation()
        {
            if (!_isValidationEnabled)
                return;

            _isValidationEnabled = false;
            _validationTimer?.Dispose();
            _validationTimer = null;
        }

        /// <summary>
        /// Manually validates the license
        /// </summary>
        public async Task<LicenseValidationResponse> ValidateLicenseAsync()
        {
            try
            {
                if (!_authenticationService.IsAuthenticated || !_networkManager.IsConnected)
                {
                    var response = LicenseValidationResponse.Invalid("Not authenticated or connected");
                    _lastValidationResponse = response;
                    _lastValidationTime = DateTime.UtcNow;
                    OnLicenseValidated(response, false);
                    return response;
                }

                var validationResponse = await _networkManager.ValidateLicenseAsync();
                _lastValidationResponse = validationResponse;
                _lastValidationTime = DateTime.UtcNow;

                if (validationResponse.IsValid)
                {
                    OnLicenseValidated(validationResponse, true);
                    
                    // Check for expiration warnings
                    CheckForExpirationWarnings(validationResponse);
                }
                else
                {
                    OnLicenseValidated(validationResponse, false);
                    OnLicenseExpired(validationResponse.Message);
                }

                return validationResponse;
            }
            catch (Exception ex)
            {
                var errorResponse = LicenseValidationResponse.Invalid($"Validation error: {ex.Message}");
                _lastValidationResponse = errorResponse;
                _lastValidationTime = DateTime.UtcNow;
                OnLicenseValidated(errorResponse, false);
                return errorResponse;
            }
        }

        /// <summary>
        /// Gets the current license status
        /// </summary>
        public LicenseStatus GetLicenseStatus()
        {
            if (!_authenticationService.IsAuthenticated)
                return LicenseStatus.NotAuthenticated;

            if (_lastValidationResponse == null)
                return LicenseStatus.Unknown;

            if (!_lastValidationResponse.IsValid)
                return LicenseStatus.Invalid;

            if (_lastValidationResponse.ExpirationDate.HasValue)
            {
                var daysRemaining = (_lastValidationResponse.ExpirationDate.Value - DateTime.UtcNow).TotalDays;
                
                if (daysRemaining <= 0)
                    return LicenseStatus.Expired;
                
                if (daysRemaining <= 7)
                    return LicenseStatus.ExpiringSoon;
                
                if (daysRemaining <= 30)
                    return LicenseStatus.ExpiringThisMonth;
            }

            return LicenseStatus.Valid;
        }

        /// <summary>
        /// Gets the number of days until license expiration
        /// </summary>
        public int GetDaysUntilExpiration()
        {
            if (_lastValidationResponse?.ExpirationDate == null)
                return -1;

            var days = (int)(_lastValidationResponse.ExpirationDate.Value - DateTime.UtcNow).TotalDays;
            return Math.Max(0, days);
        }

        /// <summary>
        /// Gets a user-friendly license status message
        /// </summary>
        public string GetLicenseStatusMessage()
        {
            var status = GetLicenseStatus();
            var daysRemaining = GetDaysUntilExpiration();

            switch (status)
            {
                case LicenseStatus.NotAuthenticated:
                    return "Not authenticated";
                
                case LicenseStatus.Unknown:
                    return "License status unknown";
                
                case LicenseStatus.Invalid:
                    return _lastValidationResponse?.Message ?? "License is invalid";
                
                case LicenseStatus.Expired:
                    return "License has expired";
                
                case LicenseStatus.ExpiringSoon:
                    return $"License expires in {daysRemaining} day{(daysRemaining == 1 ? "" : "s")}";
                
                case LicenseStatus.ExpiringThisMonth:
                    return $"License expires in {daysRemaining} days";
                
                case LicenseStatus.Valid:
                    if (daysRemaining > 0)
                        return $"License is valid ({daysRemaining} days remaining)";
                    else
                        return "License is valid";
                
                default:
                    return "Unknown license status";
            }
        }

        /// <summary>
        /// Checks if the license requires immediate attention
        /// </summary>
        public bool RequiresAttention()
        {
            var status = GetLicenseStatus();
            return status == LicenseStatus.Expired || 
                   status == LicenseStatus.ExpiringSoon || 
                   status == LicenseStatus.Invalid ||
                   status == LicenseStatus.NotAuthenticated;
        }

        /// <summary>
        /// Timer callback for automatic license validation
        /// </summary>
        private async void ValidateLicenseCallback(object state)
        {
            if (!_isValidationEnabled)
                return;

            try
            {
                await ValidateLicenseAsync();
            }
            catch (Exception)
            {
                // Ignore validation errors in background
            }
        }

        /// <summary>
        /// Checks for expiration warnings and raises appropriate events
        /// </summary>
        private void CheckForExpirationWarnings(LicenseValidationResponse response)
        {
            if (!response.ExpirationDate.HasValue)
                return;

            var daysRemaining = (int)(response.ExpirationDate.Value - DateTime.UtcNow).TotalDays;
            
            if (daysRemaining <= 1)
            {
                OnLicenseWarning($"License expires in {daysRemaining} day{(daysRemaining == 1 ? "" : "s")}!", LicenseWarningLevel.Critical);
            }
            else if (daysRemaining <= 7)
            {
                OnLicenseWarning($"License expires in {daysRemaining} days", LicenseWarningLevel.High);
            }
            else if (daysRemaining <= 30)
            {
                OnLicenseWarning($"License expires in {daysRemaining} days", LicenseWarningLevel.Medium);
            }
        }

        /// <summary>
        /// Handles authentication state changes
        /// </summary>
        private void AuthenticationService_AuthenticationStateChanged(object sender, AuthenticationEventArgs e)
        {
            if (e.IsAuthenticated)
            {
                // Start validation when authenticated
                if (!_isValidationEnabled)
                {
                    StartValidation();
                }
            }
            else
            {
                // Stop validation when not authenticated
                StopValidation();
                _lastValidationResponse = null;
                _lastValidationTime = null;
            }
        }

        /// <summary>
        /// Raises the license validated event
        /// </summary>
        private void OnLicenseValidated(LicenseValidationResponse response, bool isValid)
        {
            LicenseValidated?.Invoke(this, new LicenseValidationEventArgs
            {
                Response = response,
                IsValid = isValid,
                ValidationTime = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Raises the license expired event
        /// </summary>
        private void OnLicenseExpired(string reason)
        {
            LicenseExpired?.Invoke(this, new LicenseExpiredEventArgs
            {
                Reason = reason,
                ExpiredTime = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Raises the license warning event
        /// </summary>
        private void OnLicenseWarning(string message, LicenseWarningLevel level)
        {
            LicenseWarning?.Invoke(this, new LicenseWarningEventArgs
            {
                Message = message,
                Level = level,
                WarningTime = DateTime.UtcNow
            });
        }

        public void Dispose()
        {
            StopValidation();
            _authenticationService.AuthenticationStateChanged -= AuthenticationService_AuthenticationStateChanged;
        }
    }

    /// <summary>
    /// License status enumeration
    /// </summary>
    public enum LicenseStatus
    {
        Unknown,
        NotAuthenticated,
        Valid,
        Invalid,
        Expired,
        ExpiringSoon,
        ExpiringThisMonth
    }

    /// <summary>
    /// License warning levels
    /// </summary>
    public enum LicenseWarningLevel
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Event arguments for license validation events
    /// </summary>
    public class LicenseValidationEventArgs : EventArgs
    {
        public LicenseValidationResponse Response { get; set; }
        public bool IsValid { get; set; }
        public DateTime ValidationTime { get; set; }
    }

    public class LicenseExpiredEventArgs : EventArgs
    {
        public string Reason { get; set; }
        public DateTime ExpiredTime { get; set; }
    }

    public class LicenseWarningEventArgs : EventArgs
    {
        public string Message { get; set; }
        public LicenseWarningLevel Level { get; set; }
        public DateTime WarningTime { get; set; }
    }
}
