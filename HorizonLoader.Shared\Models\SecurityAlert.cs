using System;

namespace HorizonLoader.Shared.Models
{
    /// <summary>
    /// Represents a security alert sent from client to server when tampering or debugging is detected.
    /// </summary>
    public class SecurityAlert
    {
        /// <summary>
        /// Serial key of the authenticated user (null for pre-authentication detections).
        /// </summary>
        public string Serial<PERSON>ey { get; set; }

        /// <summary>
        /// Hardware identifier of the system where the detection occurred.
        /// </summary>
        public string Hwid { get; set; }

        /// <summary>
        /// Type of detection that was triggered (debugger, process injection, timing attack, etc.).
        /// </summary>
        public string DetectionType { get; set; }

        /// <summary>
        /// UTC timestamp when the detection happened.
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Public IP address of the client as seen by the client at the time of detection.
        /// </summary>
        public string ClientIp { get; set; }
    }
}