using System;
using System.Windows.Forms;
using HorizonLoader.Server.Services;

namespace HorizonLoader.Server.Forms
{
    /// <summary>
    /// Form for configuring server settings
    /// </summary>
    public partial class ConfigurationForm : Form
    {
        private readonly DatabaseService _databaseService;
        private readonly LoggingService _loggingService;
        private ServerConfiguration _configuration;

        public ConfigurationForm(DatabaseService databaseService, LoggingService loggingService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));

            InitializeComponent();
            LoadConfiguration();
        }

        private void LoadConfiguration()
        {
            try
            {
                _configuration = _databaseService.GetConfiguration();

                // Load values into controls
                chkEnableAccountLocking.Checked = _configuration.EnableAccountLocking;
                nudMaxLoginAttempts.Value = _configuration.MaxLoginAttempts;
                nudLockoutDuration.Value = _configuration.LockoutDurationMinutes;
                nudSessionTimeout.Value = _configuration.SessionTimeoutMinutes;
                chkEnableLogging.Checked = _configuration.EnableLogging;
                chkMaintenanceMode.Checked = _configuration.MaintenanceMode;
                txtAdminPassword.Text = _configuration.AdminPassword;
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error loading configuration", ex);
                MessageBox.Show($"Error loading configuration: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtAdminPassword.Text))
                {
                    MessageBox.Show("Admin password cannot be empty.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtAdminPassword.Focus();
                    return;
                }

                if (txtAdminPassword.Text.Length < 6)
                {
                    MessageBox.Show("Admin password must be at least 6 characters long.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtAdminPassword.Focus();
                    return;
                }

                // Update configuration
                _configuration.EnableAccountLocking = chkEnableAccountLocking.Checked;
                _configuration.MaxLoginAttempts = (int)nudMaxLoginAttempts.Value;
                _configuration.LockoutDurationMinutes = (int)nudLockoutDuration.Value;
                _configuration.SessionTimeoutMinutes = (int)nudSessionTimeout.Value;
                _configuration.EnableLogging = chkEnableLogging.Checked;
                _configuration.MaintenanceMode = chkMaintenanceMode.Checked;
                _configuration.AdminPassword = txtAdminPassword.Text;

                // Save configuration
                if (_databaseService.UpdateConfiguration(_configuration))
                {
                    _loggingService.LogInfo("Server configuration updated");
                    MessageBox.Show("Configuration saved successfully!", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Failed to save configuration.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error saving configuration", ex);
                MessageBox.Show($"Error saving configuration: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnResetToDefaults_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to reset all settings to default values?", 
                "Reset to Defaults", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // Reset to default values
                chkEnableAccountLocking.Checked = true;
                nudMaxLoginAttempts.Value = 3;
                nudLockoutDuration.Value = 5;
                nudSessionTimeout.Value = 30;
                chkEnableLogging.Checked = true;
                chkMaintenanceMode.Checked = false;
                txtAdminPassword.Text = "admin123";

                MessageBox.Show("Settings reset to default values.", "Reset Complete", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void chkEnableAccountLocking_CheckedChanged(object sender, EventArgs e)
        {
            // Enable/disable related controls
            nudMaxLoginAttempts.Enabled = chkEnableAccountLocking.Checked;
            nudLockoutDuration.Enabled = chkEnableAccountLocking.Checked;
            lblMaxLoginAttempts.Enabled = chkEnableAccountLocking.Checked;
            lblLockoutDuration.Enabled = chkEnableAccountLocking.Checked;
        }

        private void btnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                // Test database connection
                var stats = _databaseService.GetStatistics();
                MessageBox.Show($"Database connection successful!\n\nStatistics:\n" +
                    $"Total Serial Keys: {stats.TotalSerialKeys}\n" +
                    $"Total User Accounts: {stats.TotalUserAccounts}\n" +
                    $"Last Modified: {stats.LastModified}", 
                    "Connection Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Database connection test failed", ex);
                MessageBox.Show($"Database connection test failed: {ex.Message}", "Connection Test Failed", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnViewDatabaseInfo_Click(object sender, EventArgs e)
        {
            try
            {
                var stats = _databaseService.GetStatistics();
                
                string info = $"Database Information:\n\n" +
                    $"Total Serial Keys: {stats.TotalSerialKeys}\n" +
                    $"Active Serial Keys: {stats.ActiveSerialKeys}\n" +
                    $"Expired Serial Keys: {stats.ExpiredSerialKeys}\n" +
                    $"Total User Accounts: {stats.TotalUserAccounts}\n" +
                    $"Active User Accounts: {stats.ActiveUserAccounts}\n" +
                    $"Locked User Accounts: {stats.LockedUserAccounts}\n" +
                    $"Last Modified: {stats.LastModified:yyyy-MM-dd HH:mm:ss}";

                MessageBox.Show(info, "Database Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error getting database information", ex);
                MessageBox.Show($"Error getting database information: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void chkMaintenanceMode_CheckedChanged(object sender, EventArgs e)
        {
            if (chkMaintenanceMode.Checked)
            {
                var result = MessageBox.Show("Enabling maintenance mode will prevent all client connections. Continue?", 
                    "Maintenance Mode", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.No)
                {
                    chkMaintenanceMode.Checked = false;
                }
            }
        }
    }
}
