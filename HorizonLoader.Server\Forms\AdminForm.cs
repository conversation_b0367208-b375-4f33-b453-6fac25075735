using System;
using System.Configuration;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using HorizonLoader.Shared.Security;
using HorizonLoader.Server.Network;
using HorizonLoader.Server.Services;

namespace HorizonLoader.Server.Forms
{
    /// <summary>
    /// Main admin form for the HorizonLoader Server
    /// </summary>
    public partial class AdminForm : Form
    {
        private readonly DatabaseService _databaseService;
        private readonly SerialKeyService _serialKeyService;
        private readonly UserManagementService _userManagementService;
        private readonly LoggingService _loggingService;
        private ServerNetworkManager _networkManager;
        private Timer _refreshTimer;
        private byte[] _encryptionKey;

        public AdminForm(DatabaseService databaseService, SerialKeyService serialKeyService, 
            UserManagementService userManagementService, LoggingService loggingService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _serialKeyService = serialKeyService ?? throw new ArgumentNullException(nameof(serialKeyService));
            _userManagementService = userManagementService ?? throw new ArgumentNullException(nameof(userManagementService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));

            InitializeComponent();
            SetupEnhancedUI();
            InitializeForm();
        }

        /// <summary>
        /// Dynamically adds enhanced UI tabs and controls (Active Connections & Security Alerts).
        /// </summary>
        private void SetupEnhancedUI()
        {
            try
            {
                // Active Connections tab
                var tabConnections = new TabPage("Active Connections") { Name = "tabConnections" };
                var dgvConnections = new DataGridView { Name = "dgvConnections", Dock = DockStyle.Fill, ReadOnly = true, AllowUserToAddRows = false };
                tabConnections.Controls.Add(dgvConnections);
                tabControl1.Controls.Add(tabConnections);

                // Security Alerts tab
                var tabSecurity = new TabPage("Security Alerts") { Name = "tabSecurity" };
                var dgvSecurity = new DataGridView { Name = "dgvSecurityAlerts", Dock = DockStyle.Fill, ReadOnly = true, AllowUserToAddRows = false };
                tabSecurity.Controls.Add(dgvSecurity);
                tabControl1.Controls.Add(tabSecurity);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to set up enhanced UI: {ex.Message}", "UI Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void InitializeForm()
        {
            try
            {
                // Generate encryption key for network communication
                _encryptionKey = EncryptionHelper.GenerateKey();

                // Initialize network manager
                int port = int.Parse(ConfigurationManager.AppSettings["ListenPort"] ?? "8080");
                _networkManager = new ServerNetworkManager(port, _encryptionKey, _userManagementService, _serialKeyService, _loggingService);
                
                // Subscribe to network events
                _networkManager.ClientConnected += NetworkManager_ClientConnected;
                _networkManager.ClientDisconnected += NetworkManager_ClientDisconnected;

                // Initialize refresh timer
                _refreshTimer = new Timer();
                _refreshTimer.Interval = 5000; // 5 seconds
                _refreshTimer.Tick += RefreshTimer_Tick;
                _refreshTimer.Start();

                // Load initial data
                RefreshData();

                _loggingService.LogInfo("Admin form initialized successfully");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error initializing admin form", ex);
                MessageBox.Show($"Error initializing admin form: {ex.Message}", "Initialization Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void NetworkManager_ClientConnected(object sender, ClientConnectedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => NetworkManager_ClientConnected(sender, e)));
                return;
            }

            UpdateConnectionStatus();
            _loggingService.LogNetworkEvent($"Client connected: {e.Connection.ClientIp}");
        }

        private void NetworkManager_ClientDisconnected(object sender, ClientDisconnectedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => NetworkManager_ClientDisconnected(sender, e)));
                return;
            }

            UpdateConnectionStatus();
            _loggingService.LogNetworkEvent($"Client disconnected: {e.Connection.ClientIp}");
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            RefreshData();
        }

        private void RefreshData()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(RefreshData));
                    return;
                }

                // Update statistics
                UpdateStatistics();
                
                // Update user accounts grid
                UpdateUserAccountsGrid();
                
                // Update serial keys grid
                UpdateSerialKeysGrid();
                
                // Update active connections grid
                UpdateActiveConnectionsGrid();

                // Update security alerts grid
                UpdateSecurityAlertsGrid();

                // Update connection status
                UpdateConnectionStatus();
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error refreshing data", ex);
            }
        }

        private void UpdateStatistics()
        {
            var dbStats = _databaseService.GetStatistics();
            var userStats = _userManagementService.GetUserAccountStatistics();

            lblTotalSerialKeys.Text = dbStats.TotalSerialKeys.ToString();
            lblActiveSerialKeys.Text = dbStats.ActiveSerialKeys.ToString();
            lblExpiredSerialKeys.Text = dbStats.ExpiredSerialKeys.ToString();
            lblTotalUsers.Text = userStats.TotalAccounts.ToString();
            lblActiveUsers.Text = userStats.ActiveAccounts.ToString();
            lblLockedUsers.Text = userStats.LockedAccounts.ToString();
            lblOnlineUsers.Text = userStats.OnlineAccounts.ToString();
        }

        private void UpdateUserAccountsGrid()
        {
            var users = _userManagementService.GetUserAccounts();
            
            dgvUserAccounts.DataSource = users.Select(u => new
            {
                SerialKey = u.SerialKey,
                HWID = u.Hwid?.Substring(0, Math.Min(8, u.Hwid.Length)) + "...",
                IsActive = u.IsActive ? "Yes" : "No",
                IsLocked = u.IsLocked ? "Yes" : "No",
                LastLogin = u.LastLoginDate?.ToString("yyyy-MM-dd HH:mm") ?? "Never",
                LastIP = u.LastLoginIp ?? "N/A",
                Online = u.IsCurrentlyOnline ? "Yes" : "No",
                Created = u.CreatedDate.ToString("yyyy-MM-dd")
            }).ToList();

            // Auto-resize columns
            dgvUserAccounts.AutoResizeColumns();
        }

        private void UpdateActiveConnectionsGrid()
        {
            if (tabControl1.Controls.Find("dgvConnections", true).FirstOrDefault() is DataGridView dgv)
            {
                var connections = _networkManager?.GetActiveConnections() ?? new System.Collections.Generic.List<ClientConnection>();
                dgv.DataSource = connections.Select(c => new
                {
                    IP = c.ClientIp,
                    SerialKey = c.SerialKey ?? "N/A",
                    ConnectedAt = c.ConnectedTime.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    SessionDuration = (DateTime.UtcNow - c.ConnectedTime).ToString("g"),
                    Authenticated = c.IsAuthenticated ? "Yes" : "No"
                }).ToList();
                dgv.AutoResizeColumns();
            }
        }

        private void UpdateSecurityAlertsGrid()
        {
            if (tabControl1.Controls.Find("dgvSecurityAlerts", true).FirstOrDefault() is DataGridView dgv)
            {
                var recentLogs = _loggingService.GetRecentLogEntries(500)
                                               .Where(l => l.Contains("SECURITY:"))
                                               .Select(l => new { Raw = l });
                dgv.DataSource = recentLogs.ToList();
                dgv.AutoResizeColumns();
            }
        }

        private void UpdateSerialKeysGrid()
        {
            var serialKeys = _databaseService.GetAllSerialKeys();
            
            dgvSerialKeys.DataSource = serialKeys.Select(k => new
            {
                Key = k.Key,
                ProductID = k.ProductId,
                ProductName = k.ProductName,
                IsActive = k.IsActive ? "Yes" : "No",
                Expires = k.ExpirationDate.ToString("yyyy-MM-dd"),
                Status = k.IsExpired() ? "Expired" : k.IsValid() ? "Valid" : "Invalid",
                BoundHWID = !string.IsNullOrEmpty(k.BoundHwid) ? "Yes" : "No",
                ResetCount = $"{k.HwidResetCount}/{k.MaxHwidResets}",
                Created = k.CreatedDate.ToString("yyyy-MM-dd")
            }).ToList();

            // Auto-resize columns
            dgvSerialKeys.AutoResizeColumns();
        }

        private void UpdateConnectionStatus()
        {
            if (_networkManager != null)
            {
                lblServerStatus.Text = _networkManager.IsRunning ? "Running" : "Stopped";
                lblServerStatus.ForeColor = _networkManager.IsRunning ? Color.Green : Color.Red;
                lblActiveConnections.Text = _networkManager.ActiveConnectionCount.ToString();
                
                btnStartServer.Enabled = !_networkManager.IsRunning;
                btnStopServer.Enabled = _networkManager.IsRunning;
            }
        }

        private async void btnStartServer_Click(object sender, EventArgs e)
        {
            try
            {
                btnStartServer.Enabled = false;
                await _networkManager.StartAsync();
                _loggingService.LogInfo("Server started by admin");
                UpdateConnectionStatus();
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error starting server", ex);
                MessageBox.Show($"Error starting server: {ex.Message}", "Server Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                btnStartServer.Enabled = true;
            }
        }

        private async void btnStopServer_Click(object sender, EventArgs e)
        {
            try
            {
                btnStopServer.Enabled = false;
                await _networkManager.StopAsync();
                _loggingService.LogInfo("Server stopped by admin");
                UpdateConnectionStatus();
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error stopping server", ex);
                MessageBox.Show($"Error stopping server: {ex.Message}", "Server Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                btnStopServer.Enabled = true;
            }
        }

        private void btnGenerateSerialKey_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new SerialKeyGeneratorForm(_serialKeyService, _loggingService))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        RefreshData();
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error opening serial key generator", ex);
                MessageBox.Show($"Error opening serial key generator: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnConfiguration_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new ConfigurationForm(_databaseService, _loggingService))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        RefreshData();
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error opening configuration form", ex);
                MessageBox.Show($"Error opening configuration form: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnResetHWID_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvUserAccounts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Please select a user account first.", "No Selection", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedRow = dgvUserAccounts.SelectedRows[0];
                string serialKey = selectedRow.Cells["SerialKey"].Value?.ToString();

                if (string.IsNullOrEmpty(serialKey))
                {
                    MessageBox.Show("Invalid serial key selected.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var result = MessageBox.Show($"Are you sure you want to reset HWID for serial key {serialKey}?", 
                    "Confirm HWID Reset", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var config = _databaseService.GetConfiguration();
                    var resetResult = _serialKeyService.ResetHwid(serialKey, config.AdminPassword);

                    if (resetResult.Success)
                    {
                        MessageBox.Show(resetResult.Message, "Success", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        RefreshData();
                    }
                    else
                    {
                        MessageBox.Show(resetResult.ErrorMessage, "Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error resetting HWID", ex);
                MessageBox.Show($"Error resetting HWID: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUnlockUser_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvUserAccounts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Please select a user account first.", "No Selection", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedRow = dgvUserAccounts.SelectedRows[0];
                string serialKey = selectedRow.Cells["SerialKey"].Value?.ToString();

                if (string.IsNullOrEmpty(serialKey))
                {
                    MessageBox.Show("Invalid serial key selected.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (_userManagementService.UnlockUserAccount(serialKey))
                {
                    MessageBox.Show("User account unlocked successfully.", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    RefreshData();
                }
                else
                {
                    MessageBox.Show("Failed to unlock user account.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error unlocking user account", ex);
                MessageBox.Show($"Error unlocking user account: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnViewLogs_Click(object sender, EventArgs e)
        {
            try
            {
                var recentLogs = _loggingService.GetRecentLogEntries(200);
                string logText = string.Join(Environment.NewLine, recentLogs);

                using (var form = new Form())
                {
                    form.Text = "Recent Log Entries";
                    form.Size = new Size(800, 600);
                    form.StartPosition = FormStartPosition.CenterParent;

                    var textBox = new TextBox
                    {
                        Multiline = true,
                        ScrollBars = ScrollBars.Both,
                        Dock = DockStyle.Fill,
                        Text = logText,
                        ReadOnly = true,
                        Font = new Font("Consolas", 9)
                    };

                    form.Controls.Add(textBox);
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error viewing logs", ex);
                MessageBox.Show($"Error viewing logs: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                _refreshTimer?.Stop();
                _refreshTimer?.Dispose();

                if (_networkManager != null && _networkManager.IsRunning)
                {
                    var result = MessageBox.Show("The server is still running. Do you want to stop it before closing?", 
                        "Server Running", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                    if (result == DialogResult.Cancel)
                    {
                        e.Cancel = true;
                        return;
                    }

                    if (result == DialogResult.Yes)
                    {
                        _networkManager.StopAsync().Wait(5000); // Wait up to 5 seconds
                    }
                }

                _networkManager?.Dispose();
                _loggingService.LogInfo("Admin form closing");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error closing admin form", ex);
            }

            base.OnFormClosing(e);
        }
    }
}
