{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.client\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|solutionrelative:horizonloader.client\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C3D4E5F6-0000-0000-0000-000000000000}|HorizonLoader.Shared\\HorizonLoader.Shared.csproj|c:\\users\\<USER>\\onedrive\\masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.shared\\security\\encryptionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3D4E5F6-0000-0000-0000-000000000000}|HorizonLoader.Shared\\HorizonLoader.Shared.csproj|solutionrelative:horizonloader.shared\\security\\encryptionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.client\\forms\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|solutionrelative:horizonloader.client\\forms\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.client\\forms\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|solutionrelative:horizonloader.client\\forms\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|c:\\users\\<USER>\\onedrive\\masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\network\\servernetworkmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\network\\servernetworkmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.client\\services\\authenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|solutionrelative:horizonloader.client\\services\\authenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C3D4E5F6-0000-0000-0000-000000000000}|HorizonLoader.Shared\\HorizonLoader.Shared.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.shared\\security\\antidebug.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3D4E5F6-0000-0000-0000-000000000000}|HorizonLoader.Shared\\HorizonLoader.Shared.csproj|solutionrelative:horizonloader.shared\\security\\antidebug.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\services\\usermanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\services\\usermanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\services\\serialkeyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\services\\serialkeyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\services\\loggingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\services\\loggingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\forms\\serialkeygeneratorform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\forms\\serialkeygeneratorform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\forms\\configurationform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\forms\\configurationform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.server\\forms\\adminform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{B2C3D4E5-0000-0000-0000-000000000000}|HorizonLoader.Server\\HorizonLoader.Server.csproj|solutionrelative:horizonloader.server\\forms\\adminform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\horizonloader.client\\network\\clientnetworkmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-0000-0000-0000-000000000000}|HorizonLoader.Client\\HorizonLoader.Client.csproj|solutionrelative:horizonloader.client\\network\\clientnetworkmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "EncryptionHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Shared\\Security\\EncryptionHelper.cs", "RelativeDocumentMoniker": "HorizonLoader.Shared\\Security\\EncryptionHelper.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Shared\\Security\\EncryptionHelper.cs", "RelativeToolTip": "HorizonLoader.Shared\\Security\\EncryptionHelper.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T10:07:14.233Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "UserManagementService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\UserManagementService.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Services\\UserManagementService.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\UserManagementService.cs", "RelativeToolTip": "HorizonLoader.Server\\Services\\UserManagementService.cs", "ViewState": "AgIAADQAAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:32.58Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "SerialKeyService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\SerialKeyService.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Services\\SerialKeyService.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\SerialKeyService.cs", "RelativeToolTip": "HorizonLoader.Server\\Services\\SerialKeyService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:32Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "LoggingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\LoggingService.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Services\\LoggingService.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\LoggingService.cs", "RelativeToolTip": "HorizonLoader.Server\\Services\\LoggingService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:31.478Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "DatabaseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\DatabaseService.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Services\\DatabaseService.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Services\\DatabaseService.cs", "RelativeToolTip": "HorizonLoader.Server\\Services\\DatabaseService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:30.691Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ServerNetworkManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Network\\ServerNetworkManager.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Network\\ServerNetworkManager.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Network\\ServerNetworkManager.cs", "RelativeToolTip": "HorizonLoader.Server\\Network\\ServerNetworkManager.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAABEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:26.775Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "AuthenticationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Services\\AuthenticationService.cs", "RelativeDocumentMoniker": "HorizonLoader.Client\\Services\\AuthenticationService.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Services\\AuthenticationService.cs", "RelativeToolTip": "HorizonLoader.Client\\Services\\AuthenticationService.cs", "ViewState": "AgIAACQAAAAAAAAAAAAmwEkAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:52:43.736Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "LoginForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Forms\\LoginForm.cs", "RelativeDocumentMoniker": "HorizonLoader.Client\\Forms\\LoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Forms\\LoginForm.cs [Design]", "RelativeToolTip": "HorizonLoader.Client\\Forms\\LoginForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:21:15.898Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Program.cs", "RelativeDocumentMoniker": "HorizonLoader.Client\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Program.cs", "RelativeToolTip": "HorizonLoader.Client\\Program.cs", "ViewState": "AgIAAHAAAAAAAAAAAAAewIQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:37.232Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Forms\\MainForm.cs", "RelativeDocumentMoniker": "HorizonLoader.Client\\Forms\\MainForm.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Forms\\MainForm.cs [Design]", "RelativeToolTip": "HorizonLoader.Client\\Forms\\MainForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:21:23.427Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 7, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Program.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Program.cs", "RelativeToolTip": "HorizonLoader.Server\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:34.806Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "AntiDebug.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Shared\\Security\\AntiDebug.cs", "RelativeDocumentMoniker": "HorizonLoader.Shared\\Security\\AntiDebug.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Shared\\Security\\AntiDebug.cs", "RelativeToolTip": "HorizonLoader.Shared\\Security\\AntiDebug.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwFQAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:49.351Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "SerialKeyGeneratorForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Forms\\SerialKeyGeneratorForm.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Forms\\SerialKeyGeneratorForm.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Forms\\SerialKeyGeneratorForm.cs [Design]", "RelativeToolTip": "HorizonLoader.Server\\Forms\\SerialKeyGeneratorForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:16.426Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "ConfigurationForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Forms\\ConfigurationForm.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Forms\\ConfigurationForm.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Forms\\ConfigurationForm.cs [Design]", "RelativeToolTip": "HorizonLoader.Server\\Forms\\ConfigurationForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:22:09.908Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "AdminForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Forms\\AdminForm.cs", "RelativeDocumentMoniker": "HorizonLoader.Server\\Forms\\AdminForm.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Server\\Forms\\AdminForm.cs [Design]", "RelativeToolTip": "HorizonLoader.Server\\Forms\\AdminForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:21:59.637Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "ClientNetworkManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Network\\ClientNetworkManager.cs", "RelativeDocumentMoniker": "HorizonLoader.Client\\Network\\ClientNetworkManager.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\augment loader\\HorizonLoader.Client\\Network\\ClientNetworkManager.cs", "RelativeToolTip": "HorizonLoader.Client\\Network\\ClientNetworkManager.cs", "ViewState": "AgIAAAoBAAAAAAAAAAAYwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T09:21:28.741Z"}]}]}]}