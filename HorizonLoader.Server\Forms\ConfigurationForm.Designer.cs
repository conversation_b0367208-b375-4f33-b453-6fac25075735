namespace HorizonLoader.Server.Forms
{
    partial class ConfigurationForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.nudLockoutDuration = new System.Windows.Forms.NumericUpDown();
            this.lblLockoutDuration = new System.Windows.Forms.Label();
            this.nudMaxLoginAttempts = new System.Windows.Forms.NumericUpDown();
            this.lblMaxLoginAttempts = new System.Windows.Forms.Label();
            this.chkEnableAccountLocking = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.nudSessionTimeout = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chkEnableLogging = new System.Windows.Forms.CheckBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.txtAdminPassword = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.chkMaintenanceMode = new System.Windows.Forms.CheckBox();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnResetToDefaults = new System.Windows.Forms.Button();
            this.btnTestConnection = new System.Windows.Forms.Button();
            this.btnViewDatabaseInfo = new System.Windows.Forms.Button();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudLockoutDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudMaxLoginAttempts)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudSessionTimeout)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.nudLockoutDuration);
            this.groupBox1.Controls.Add(this.lblLockoutDuration);
            this.groupBox1.Controls.Add(this.nudMaxLoginAttempts);
            this.groupBox1.Controls.Add(this.lblMaxLoginAttempts);
            this.groupBox1.Controls.Add(this.chkEnableAccountLocking);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(360, 100);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Account Security";
            // 
            // nudLockoutDuration
            // 
            this.nudLockoutDuration.Location = new System.Drawing.Point(150, 70);
            this.nudLockoutDuration.Maximum = new decimal(new int[] {
            1440,
            0,
            0,
            0});
            this.nudLockoutDuration.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nudLockoutDuration.Name = "nudLockoutDuration";
            this.nudLockoutDuration.Size = new System.Drawing.Size(80, 20);
            this.nudLockoutDuration.TabIndex = 4;
            this.nudLockoutDuration.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // lblLockoutDuration
            // 
            this.lblLockoutDuration.AutoSize = true;
            this.lblLockoutDuration.Location = new System.Drawing.Point(20, 72);
            this.lblLockoutDuration.Name = "lblLockoutDuration";
            this.lblLockoutDuration.Size = new System.Drawing.Size(124, 13);
            this.lblLockoutDuration.TabIndex = 3;
            this.lblLockoutDuration.Text = "Lockout Duration (mins):";
            // 
            // nudMaxLoginAttempts
            // 
            this.nudMaxLoginAttempts.Location = new System.Drawing.Point(150, 45);
            this.nudMaxLoginAttempts.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nudMaxLoginAttempts.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nudMaxLoginAttempts.Name = "nudMaxLoginAttempts";
            this.nudMaxLoginAttempts.Size = new System.Drawing.Size(80, 20);
            this.nudMaxLoginAttempts.TabIndex = 2;
            this.nudMaxLoginAttempts.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // lblMaxLoginAttempts
            // 
            this.lblMaxLoginAttempts.AutoSize = true;
            this.lblMaxLoginAttempts.Location = new System.Drawing.Point(20, 47);
            this.lblMaxLoginAttempts.Name = "lblMaxLoginAttempts";
            this.lblMaxLoginAttempts.Size = new System.Drawing.Size(109, 13);
            this.lblMaxLoginAttempts.TabIndex = 1;
            this.lblMaxLoginAttempts.Text = "Max Login Attempts:";
            // 
            // chkEnableAccountLocking
            // 
            this.chkEnableAccountLocking.AutoSize = true;
            this.chkEnableAccountLocking.Checked = true;
            this.chkEnableAccountLocking.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkEnableAccountLocking.Location = new System.Drawing.Point(10, 20);
            this.chkEnableAccountLocking.Name = "chkEnableAccountLocking";
            this.chkEnableAccountLocking.Size = new System.Drawing.Size(140, 17);
            this.chkEnableAccountLocking.TabIndex = 0;
            this.chkEnableAccountLocking.Text = "Enable Account Locking";
            this.chkEnableAccountLocking.UseVisualStyleBackColor = true;
            this.chkEnableAccountLocking.CheckedChanged += new System.EventHandler(this.chkEnableAccountLocking_CheckedChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.nudSessionTimeout);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Location = new System.Drawing.Point(12, 118);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(360, 60);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Session Management";
            // 
            // nudSessionTimeout
            // 
            this.nudSessionTimeout.Location = new System.Drawing.Point(150, 25);
            this.nudSessionTimeout.Maximum = new decimal(new int[] {
            1440,
            0,
            0,
            0});
            this.nudSessionTimeout.Minimum = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.nudSessionTimeout.Name = "nudSessionTimeout";
            this.nudSessionTimeout.Size = new System.Drawing.Size(80, 20);
            this.nudSessionTimeout.TabIndex = 1;
            this.nudSessionTimeout.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(10, 27);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(125, 13);
            this.label3.TabIndex = 0;
            this.label3.Text = "Session Timeout (mins):";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chkEnableLogging);
            this.groupBox3.Location = new System.Drawing.Point(12, 184);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(360, 50);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "Logging";
            // 
            // chkEnableLogging
            // 
            this.chkEnableLogging.AutoSize = true;
            this.chkEnableLogging.Checked = true;
            this.chkEnableLogging.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkEnableLogging.Location = new System.Drawing.Point(10, 20);
            this.chkEnableLogging.Name = "chkEnableLogging";
            this.chkEnableLogging.Size = new System.Drawing.Size(101, 17);
            this.chkEnableLogging.TabIndex = 0;
            this.chkEnableLogging.Text = "Enable Logging";
            this.chkEnableLogging.UseVisualStyleBackColor = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.txtAdminPassword);
            this.groupBox4.Controls.Add(this.label4);
            this.groupBox4.Location = new System.Drawing.Point(12, 240);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(360, 60);
            this.groupBox4.TabIndex = 3;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "Admin Settings";
            // 
            // txtAdminPassword
            // 
            this.txtAdminPassword.Location = new System.Drawing.Point(110, 25);
            this.txtAdminPassword.Name = "txtAdminPassword";
            this.txtAdminPassword.PasswordChar = '*';
            this.txtAdminPassword.Size = new System.Drawing.Size(200, 20);
            this.txtAdminPassword.TabIndex = 1;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(10, 28);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(88, 13);
            this.label4.TabIndex = 0;
            this.label4.Text = "Admin Password:";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.chkMaintenanceMode);
            this.groupBox5.Location = new System.Drawing.Point(12, 306);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(360, 50);
            this.groupBox5.TabIndex = 4;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "Server Mode";
            // 
            // chkMaintenanceMode
            // 
            this.chkMaintenanceMode.AutoSize = true;
            this.chkMaintenanceMode.Location = new System.Drawing.Point(10, 20);
            this.chkMaintenanceMode.Name = "chkMaintenanceMode";
            this.chkMaintenanceMode.Size = new System.Drawing.Size(118, 17);
            this.chkMaintenanceMode.TabIndex = 0;
            this.chkMaintenanceMode.Text = "Maintenance Mode";
            this.chkMaintenanceMode.UseVisualStyleBackColor = true;
            this.chkMaintenanceMode.CheckedChanged += new System.EventHandler(this.chkMaintenanceMode_CheckedChanged);
            // 
            // btnSave
            // 
            this.btnSave.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSave.Location = new System.Drawing.Point(12, 370);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(100, 35);
            this.btnSave.TabIndex = 5;
            this.btnSave.Text = "Save";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(118, 370);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(100, 35);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnResetToDefaults
            // 
            this.btnResetToDefaults.Location = new System.Drawing.Point(224, 370);
            this.btnResetToDefaults.Name = "btnResetToDefaults";
            this.btnResetToDefaults.Size = new System.Drawing.Size(120, 35);
            this.btnResetToDefaults.TabIndex = 7;
            this.btnResetToDefaults.Text = "Reset to Defaults";
            this.btnResetToDefaults.UseVisualStyleBackColor = true;
            this.btnResetToDefaults.Click += new System.EventHandler(this.btnResetToDefaults_Click);
            // 
            // btnTestConnection
            // 
            this.btnTestConnection.Location = new System.Drawing.Point(12, 415);
            this.btnTestConnection.Name = "btnTestConnection";
            this.btnTestConnection.Size = new System.Drawing.Size(120, 30);
            this.btnTestConnection.TabIndex = 8;
            this.btnTestConnection.Text = "Test Connection";
            this.btnTestConnection.UseVisualStyleBackColor = true;
            this.btnTestConnection.Click += new System.EventHandler(this.btnTestConnection_Click);
            // 
            // btnViewDatabaseInfo
            // 
            this.btnViewDatabaseInfo.Location = new System.Drawing.Point(138, 415);
            this.btnViewDatabaseInfo.Name = "btnViewDatabaseInfo";
            this.btnViewDatabaseInfo.Size = new System.Drawing.Size(120, 30);
            this.btnViewDatabaseInfo.TabIndex = 9;
            this.btnViewDatabaseInfo.Text = "Database Info";
            this.btnViewDatabaseInfo.UseVisualStyleBackColor = true;
            this.btnViewDatabaseInfo.Click += new System.EventHandler(this.btnViewDatabaseInfo_Click);
            // 
            // ConfigurationForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(384, 461);
            this.Controls.Add(this.btnViewDatabaseInfo);
            this.Controls.Add(this.btnTestConnection);
            this.Controls.Add(this.btnResetToDefaults);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ConfigurationForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Server Configuration";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudLockoutDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudMaxLoginAttempts)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudSessionTimeout)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown nudLockoutDuration;
        private System.Windows.Forms.Label lblLockoutDuration;
        private System.Windows.Forms.NumericUpDown nudMaxLoginAttempts;
        private System.Windows.Forms.Label lblMaxLoginAttempts;
        private System.Windows.Forms.CheckBox chkEnableAccountLocking;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.NumericUpDown nudSessionTimeout;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox chkEnableLogging;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.TextBox txtAdminPassword;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.CheckBox chkMaintenanceMode;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnResetToDefaults;
        private System.Windows.Forms.Button btnTestConnection;
        private System.Windows.Forms.Button btnViewDatabaseInfo;
    }
}
