using System;
using System.Configuration;
using System.Threading;
using System.Windows.Forms;
using HorizonLoader.Shared.Security;
using HorizonLoader.Client.Forms;
using HorizonLoader.Client.Network;
using HorizonLoader.Client.Services;

namespace HorizonLoader.Client
{
    /// <summary>
    /// Main entry point for the HorizonLoader Client application
    /// </summary>
    internal static class Program
    {
        private static ClientNetworkManager _networkManager;
        private static AuthenticationService _authenticationService;
        private static LicenseService _licenseService;
        private static Mutex _applicationMutex;

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // Ensure only one instance of the client is running
                if (!EnsureSingleInstance())
                {
                    MessageBox.Show("HorizonLoader Client is already running.", "Client Already Running", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Initialize anti-debugging protection
                InitializeSecurity();

                // Configure application settings
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                // Initialize services
                InitializeServices();

                // Start with login form
                using (var loginForm = new LoginForm(_authenticationService))
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // Authentication successful, show main form
                        using (var mainForm = new MainForm(_authenticationService, _licenseService))
                        {
                            Application.Run(mainForm);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                HandleFatalError("Fatal error during application startup", ex);
            }
            finally
            {
                Cleanup();
            }
        }

        /// <summary>
        /// Ensures only one instance of the client application is running
        /// </summary>
        private static bool EnsureSingleInstance()
        {
            try
            {
                _applicationMutex = new Mutex(true, "HorizonLoaderClient_SingleInstance", out bool createdNew);
                return createdNew;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Initializes security features
        /// </summary>
        private static void InitializeSecurity()
        {
            try
            {
                // Initialize anti-debugging protection
                AntiDebug.Initialize();

                // Validate assembly integrity
                if (!AntiDebug.ValidateAssemblyIntegrity())
                {
                    Environment.FailFast("Assembly integrity check failed");
                }
            }
            catch (Exception ex)
            {
                HandleFatalError("Security initialization failed", ex);
            }
        }

        /// <summary>
        /// Initializes all application services
        /// </summary>
        private static void InitializeServices()
        {
            try
            {
                // Read configuration
                string serverAddress = ConfigurationManager.AppSettings["ServerAddress"] ?? "127.0.0.1";
                int serverPort = int.Parse(ConfigurationManager.AppSettings["ServerPort"] ?? "8080");
                int connectionTimeout = int.Parse(ConfigurationManager.AppSettings["ConnectionTimeout"] ?? "30000");
                int heartbeatInterval = int.Parse(ConfigurationManager.AppSettings["HeartbeatInterval"] ?? "60000");

                // Generate encryption key (in production, this should be derived from a secure source)
                byte[] encryptionKey = EncryptionHelper.GenerateKey();

                // Initialize network manager
                _networkManager = new ClientNetworkManager(serverAddress, serverPort, encryptionKey, connectionTimeout, heartbeatInterval);

                // Initialize authentication service
                _authenticationService = new AuthenticationService(_networkManager);

                // Initialize license service
                _licenseService = new LicenseService(_networkManager, _authenticationService);
            }
            catch (Exception ex)
            {
                HandleFatalError("Service initialization failed", ex);
            }
        }

        /// <summary>
        /// Handles unhandled thread exceptions
        /// </summary>
        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            try
            {
                string message = $"An unexpected error occurred:\n\n{e.Exception.Message}\n\nThe application will continue running, but some features may not work correctly.";
                MessageBox.Show(message, "Unexpected Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception)
            {
                // If we can't even show an error message, just continue
            }
        }

        /// <summary>
        /// Handles unhandled domain exceptions
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception ex)
                {
                    HandleFatalError("Unhandled domain exception", ex);
                }
                else
                {
                    HandleFatalError("Unknown unhandled domain exception", null);
                }
            }
            catch (Exception)
            {
                // Last resort error handling
                Environment.FailFast("Critical error in exception handler");
            }
        }

        /// <summary>
        /// Handles fatal errors that require application termination
        /// </summary>
        private static void HandleFatalError(string message, Exception exception)
        {
            try
            {
                string errorMessage = $"{message}";
                if (exception != null)
                {
                    errorMessage += $"\n\nError: {exception.Message}";
                }
                errorMessage += "\n\nThe application will now exit.";

                MessageBox.Show(errorMessage, "Fatal Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception)
            {
                // If we can't even show an error message, just exit
            }
            finally
            {
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Cleans up resources before application exit
        /// </summary>
        private static void Cleanup()
        {
            try
            {
                // Cleanup services
                _licenseService?.Dispose();
                _authenticationService?.Dispose();
                _networkManager?.Dispose();
                
                // Cleanup security
                AntiDebug.Cleanup();

                // Release mutex
                _applicationMutex?.ReleaseMutex();
                _applicationMutex?.Dispose();
            }
            catch (Exception)
            {
                // Ignore cleanup errors
            }
        }

        /// <summary>
        /// Gets the network manager instance
        /// </summary>
        public static ClientNetworkManager NetworkManager => _networkManager;

        /// <summary>
        /// Gets the authentication service instance
        /// </summary>
        public static AuthenticationService AuthenticationService => _authenticationService;

        /// <summary>
        /// Gets the license service instance
        /// </summary>
        public static LicenseService LicenseService => _licenseService;
    }
}
