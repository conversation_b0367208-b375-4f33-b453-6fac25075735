using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace HorizonLoader.Shared.Models
{
    /// <summary>
    /// Represents the result of an authentication attempt
    /// </summary>
    public enum AuthenticationResult
    {
        Success,
        InvalidSerialKey,
        ExpiredSerialKey,
        HwidMismatch,
        AccountLocked,
        ServerError,
        InvalidCredentials,
        InsufficientPrivileges,
        TooManyAttempts,
        MaintenanceMode
    }

    /// <summary>
    /// Represents an authentication response from server to client
    /// </summary>
    public class AuthenticationResponse
    {
        [JsonProperty("result")]
        public AuthenticationResult Result { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("sessionId")]
        public string SessionId { get; set; }

        [JsonProperty("expirationDate")]
        public DateTime? ExpirationDate { get; set; }

        [JsonProperty("productAccess")]
        public List<string> ProductAccess { get; set; }

        [JsonProperty("serverTime")]
        public DateTime ServerTime { get; set; }

        [JsonProperty("canResetHwid")]
        public bool CanResetHwid { get; set; }

        [JsonProperty("remainingHwidResets")]
        public int RemainingHwidResets { get; set; }

        [JsonProperty("lockoutEndTime")]
        public DateTime? LockoutEndTime { get; set; }

        [JsonProperty("isAdmin")]
        public bool IsAdmin { get; set; }

        public AuthenticationResponse()
        {
            ServerTime = DateTime.UtcNow;
            ProductAccess = new List<string>();
        }

        public AuthenticationResponse(AuthenticationResult result, string message = null)
        {
            Result = result;
            Message = message;
            ServerTime = DateTime.UtcNow;
            ProductAccess = new List<string>();
        }

        /// <summary>
        /// Creates a successful authentication response
        /// </summary>
        public static AuthenticationResponse Success(string sessionId, DateTime? expirationDate = null, List<string> productAccess = null)
        {
            return new AuthenticationResponse(AuthenticationResult.Success, "Authentication successful")
            {
                SessionId = sessionId,
                ExpirationDate = expirationDate,
                ProductAccess = productAccess ?? new List<string>()
            };
        }

        /// <summary>
        /// Creates a failed authentication response
        /// </summary>
        public static AuthenticationResponse Failure(AuthenticationResult result, string message)
        {
            return new AuthenticationResponse(result, message);
        }

        /// <summary>
        /// Creates an account locked response
        /// </summary>
        public static AuthenticationResponse AccountLocked(DateTime? lockoutEndTime)
        {
            return new AuthenticationResponse(AuthenticationResult.AccountLocked, "Account is temporarily locked")
            {
                LockoutEndTime = lockoutEndTime
            };
        }

        /// <summary>
        /// Creates an HWID mismatch response
        /// </summary>
        public static AuthenticationResponse HwidMismatch(bool canResetHwid, int remainingResets)
        {
            return new AuthenticationResponse(AuthenticationResult.HwidMismatch, "Hardware ID mismatch")
            {
                CanResetHwid = canResetHwid,
                RemainingHwidResets = remainingResets
            };
        }
    }

    /// <summary>
    /// Represents a license validation response
    /// </summary>
    public class LicenseValidationResponse
    {
        [JsonProperty("isValid")]
        public bool IsValid { get; set; }

        [JsonProperty("expirationDate")]
        public DateTime? ExpirationDate { get; set; }

        [JsonProperty("daysRemaining")]
        public int DaysRemaining { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("serverTime")]
        public DateTime ServerTime { get; set; }

        public LicenseValidationResponse()
        {
            ServerTime = DateTime.UtcNow;
        }

        public static LicenseValidationResponse Valid(DateTime expirationDate)
        {
            var daysRemaining = (int)(expirationDate - DateTime.UtcNow).TotalDays;
            return new LicenseValidationResponse
            {
                IsValid = true,
                ExpirationDate = expirationDate,
                DaysRemaining = Math.Max(0, daysRemaining),
                Message = $"License valid. {daysRemaining} days remaining."
            };
        }

        public static LicenseValidationResponse Invalid(string reason)
        {
            return new LicenseValidationResponse
            {
                IsValid = false,
                Message = reason
            };
        }
    }
}
