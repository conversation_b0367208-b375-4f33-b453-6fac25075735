using System;
using System.Configuration;
using System.Threading;
using System.Windows.Forms;
using HorizonLoader.Shared.Security;
using HorizonLoader.Server.Forms;
using HorizonLoader.Server.Services;

namespace HorizonLoader.Server
{
    /// <summary>
    /// Main entry point for the HorizonLoader Server application
    /// </summary>
    internal static class Program
    {
        private static DatabaseService _databaseService;
        private static LoggingService _loggingService;
        private static SerialKeyService _serialKeyService;
        private static UserManagementService _userManagementService;
        private static Mutex _applicationMutex;

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // Ensure only one instance of the server is running
                if (!EnsureSingleInstance())
                {
                    MessageBox.Show("HorizonLoader Server is already running.", "Server Already Running", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Initialize anti-debugging protection
                InitializeSecurity();

                // Configure application settings
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                // Initialize services
                InitializeServices();

                // Start the main form
                using (var mainForm = new AdminForm(_databaseService, _serialKeyService, _userManagementService, _loggingService))
                {
                    Application.Run(mainForm);
                }
            }
            catch (Exception ex)
            {
                HandleFatalError("Fatal error during application startup", ex);
            }
            finally
            {
                Cleanup();
            }
        }

        /// <summary>
        /// Ensures only one instance of the server application is running
        /// </summary>
        private static bool EnsureSingleInstance()
        {
            try
            {
                _applicationMutex = new Mutex(true, "HorizonLoaderServer_SingleInstance", out bool createdNew);
                return createdNew;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Initializes security features
        /// </summary>
        private static void InitializeSecurity()
        {
            try
            {
                // Initialize anti-debugging protection
                AntiDebug.Initialize();

                // Validate assembly integrity
                if (!AntiDebug.ValidateAssemblyIntegrity())
                {
                    Environment.FailFast("Assembly integrity check failed");
                }
            }
            catch (Exception ex)
            {
                HandleFatalError("Security initialization failed", ex);
            }
        }

        /// <summary>
        /// Initializes all application services
        /// </summary>
        private static void InitializeServices()
        {
            try
            {
                // Read configuration
                string databasePath = ConfigurationManager.AppSettings["DatabasePath"] ?? "Data\\database.json";
                string logPath = ConfigurationManager.AppSettings["LogPath"] ?? "Logs\\server.log";
                bool enableLogging = bool.Parse(ConfigurationManager.AppSettings["EnableLogging"] ?? "true");
                string logLevelStr = ConfigurationManager.AppSettings["LogLevel"] ?? "Info";
                
                LogLevel logLevel = LogLevel.Info;
                if (Enum.TryParse(logLevelStr, true, out LogLevel parsedLevel))
                {
                    logLevel = parsedLevel;
                }

                // Initialize logging service first
                _loggingService = new LoggingService(logPath, enableLogging, logLevel);
                _loggingService.LogInfo("HorizonLoader Server starting up...");

                // Initialize database service
                _databaseService = new DatabaseService(databasePath);
                _loggingService.LogInfo("Database service initialized");

                // Initialize serial key service
                _serialKeyService = new SerialKeyService(_databaseService);
                _loggingService.LogInfo("Serial key service initialized");

                // Initialize user management service
                _userManagementService = new UserManagementService(_databaseService, _serialKeyService, _loggingService);
                _loggingService.LogInfo("User management service initialized");

                _loggingService.LogInfo("All services initialized successfully");
            }
            catch (Exception ex)
            {
                HandleFatalError("Service initialization failed", ex);
            }
        }

        /// <summary>
        /// Handles unhandled thread exceptions
        /// </summary>
        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            try
            {
                _loggingService?.LogError("Unhandled thread exception", e.Exception);
                
                string message = $"An unexpected error occurred:\n\n{e.Exception.Message}\n\nThe application will continue running, but some features may not work correctly.";
                MessageBox.Show(message, "Unexpected Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception)
            {
                // If logging fails, show a basic error message
                MessageBox.Show("A critical error occurred and could not be logged.", "Critical Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles unhandled domain exceptions
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception ex)
                {
                    _loggingService?.LogError("Unhandled domain exception", ex);
                    HandleFatalError("Unhandled domain exception", ex);
                }
                else
                {
                    _loggingService?.LogError("Unknown unhandled domain exception");
                    HandleFatalError("Unknown unhandled domain exception", null);
                }
            }
            catch (Exception)
            {
                // Last resort error handling
                Environment.FailFast("Critical error in exception handler");
            }
        }

        /// <summary>
        /// Handles fatal errors that require application termination
        /// </summary>
        private static void HandleFatalError(string message, Exception exception)
        {
            try
            {
                _loggingService?.LogError(message, exception);
                
                string errorMessage = $"{message}";
                if (exception != null)
                {
                    errorMessage += $"\n\nError: {exception.Message}";
                }
                errorMessage += "\n\nThe application will now exit.";

                MessageBox.Show(errorMessage, "Fatal Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception)
            {
                // If we can't even show an error message, just exit
            }
            finally
            {
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Cleans up resources before application exit
        /// </summary>
        private static void Cleanup()
        {
            try
            {
                _loggingService?.LogInfo("HorizonLoader Server shutting down...");
                
                // Cleanup services
                _userManagementService = null;
                _serialKeyService = null;
                _databaseService = null;
                
                // Cleanup security
                AntiDebug.Cleanup();
                
                // Cleanup logging (do this last)
                _loggingService?.Dispose();
                _loggingService = null;

                // Release mutex
                _applicationMutex?.ReleaseMutex();
                _applicationMutex?.Dispose();
            }
            catch (Exception)
            {
                // Ignore cleanup errors
            }
        }

        /// <summary>
        /// Gets the database service instance
        /// </summary>
        public static DatabaseService DatabaseService => _databaseService;

        /// <summary>
        /// Gets the logging service instance
        /// </summary>
        public static LoggingService LoggingService => _loggingService;

        /// <summary>
        /// Gets the serial key service instance
        /// </summary>
        public static SerialKeyService SerialKeyService => _serialKeyService;

        /// <summary>
        /// Gets the user management service instance
        /// </summary>
        public static UserManagementService UserManagementService => _userManagementService;
    }
}
