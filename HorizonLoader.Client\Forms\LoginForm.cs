using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using HorizonLoader.Shared.Models;
using HorizonLoader.Client.Services;

namespace HorizonLoader.Client.Forms
{
    /// <summary>
    /// Login form for client authentication
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly AuthenticationService _authenticationService;
        private bool _isAuthenticating;

        public LoginForm(AuthenticationService authenticationService)
        {
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Set form properties
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;

            // Subscribe to authentication events
            _authenticationService.AuthenticationStateChanged += AuthenticationService_AuthenticationStateChanged;

            // Set focus to serial key textbox
            txtSerialKey.Focus();
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            await PerformLoginAsync();
        }

        private async void txtSerialKey_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                await PerformLoginAsync();
            }
        }

        private async Task PerformLoginAsync()
        {
            if (_isAuthenticating)
                return;

            try
            {
                string serialKey = txtSerialKey.Text.Trim();

                // Validate input
                if (string.IsNullOrEmpty(serialKey))
                {
                    MessageBox.Show("Please enter your serial key.", "Serial Key Required", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtSerialKey.Focus();
                    return;
                }

                // Start authentication
                SetAuthenticatingState(true);

                var result = await _authenticationService.AuthenticateAsync(serialKey);

                if (result.Success)
                {
                    // Authentication successful
                    ShowSuccessMessage(result);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    // Authentication failed
                    ShowErrorMessage(result);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An unexpected error occurred during authentication:\n\n{ex.Message}", 
                    "Authentication Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                SetAuthenticatingState(false);
            }
        }

        private void ShowSuccessMessage(Services.AuthenticationResult result)
        {
            string message = "Authentication successful!";
            
            if (result.ExpirationDate.HasValue)
            {
                var daysRemaining = (int)(result.ExpirationDate.Value - DateTime.UtcNow).TotalDays;
                message += $"\n\nLicense expires in {daysRemaining} day{(daysRemaining == 1 ? "" : "s")}.";
            }

            MessageBox.Show(message, "Authentication Successful", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowErrorMessage(Services.AuthenticationResult result)
        {
            string title = "Authentication Failed";
            string message = result.ErrorMessage ?? "Authentication failed for unknown reason.";
            MessageBoxIcon icon = MessageBoxIcon.Error;

            // Handle specific authentication results
            switch (result.Result)
            {
                case Shared.Models.AuthenticationResult.InvalidSerialKey:
                    title = "Invalid Serial Key";
                    message = "The serial key you entered is not valid. Please check your serial key and try again.";
                    break;

                case Shared.Models.AuthenticationResult.ExpiredSerialKey:
                    title = "Serial Key Expired";
                    message = "Your serial key has expired. Please contact support to renew your license.";
                    break;

                case Shared.Models.AuthenticationResult.HwidMismatch:
                    title = "Hardware ID Mismatch";
                    message = "This serial key is bound to different hardware.";
                    
                    if (result.CanResetHwid)
                    {
                        message += $"\n\nYou have {result.RemainingHwidResets} HWID reset{(result.RemainingHwidResets == 1 ? "" : "s")} remaining.";
                        message += "\nContact support to reset your hardware binding.";
                    }
                    else
                    {
                        message += "\nNo HWID resets remaining. Contact support for assistance.";
                    }
                    break;

                case Shared.Models.AuthenticationResult.AccountLocked:
                    title = "Account Locked";
                    message = "Your account has been temporarily locked due to too many failed login attempts.";
                    
                    if (result.LockoutEndTime.HasValue)
                    {
                        var unlockTime = result.LockoutEndTime.Value.ToLocalTime();
                        message += $"\n\nAccount will be unlocked at: {unlockTime:yyyy-MM-dd HH:mm:ss}";
                    }
                    break;

                case Shared.Models.AuthenticationResult.ServerError:
                    title = "Server Error";
                    message = "A server error occurred. Please try again later or contact support if the problem persists.";
                    break;

                case Shared.Models.AuthenticationResult.MaintenanceMode:
                    title = "Server Maintenance";
                    message = "The server is currently in maintenance mode. Please try again later.";
                    icon = MessageBoxIcon.Information;
                    break;
            }

            MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
        }

        private void SetAuthenticatingState(bool isAuthenticating)
        {
            _isAuthenticating = isAuthenticating;
            
            txtSerialKey.Enabled = !isAuthenticating;
            btnLogin.Enabled = !isAuthenticating;
            btnCancel.Enabled = !isAuthenticating;
            
            if (isAuthenticating)
            {
                btnLogin.Text = "Authenticating...";
                this.Cursor = Cursors.WaitCursor;
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
            }
            else
            {
                btnLogin.Text = "Login";
                this.Cursor = Cursors.Default;
                progressBar.Visible = false;
                progressBar.Style = ProgressBarStyle.Continuous;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnShowHardwareInfo_Click(object sender, EventArgs e)
        {
            try
            {
                string hardwareInfo = _authenticationService.GetHardwareInfo();
                
                using (var form = new Form())
                {
                    form.Text = "Hardware Information";
                    form.Size = new Size(600, 400);
                    form.StartPosition = FormStartPosition.CenterParent;
                    form.FormBorderStyle = FormBorderStyle.FixedDialog;
                    form.MaximizeBox = false;
                    form.MinimizeBox = false;

                    var textBox = new TextBox
                    {
                        Multiline = true,
                        ScrollBars = ScrollBars.Both,
                        Dock = DockStyle.Fill,
                        Text = hardwareInfo,
                        ReadOnly = true,
                        Font = new Font("Consolas", 9)
                    };

                    form.Controls.Add(textBox);
                    form.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error retrieving hardware information:\n\n{ex.Message}", 
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AuthenticationService_AuthenticationStateChanged(object sender, AuthenticationEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => AuthenticationService_AuthenticationStateChanged(sender, e)));
                return;
            }

            // Update status label
            lblStatus.Text = e.Message;
            lblStatus.ForeColor = e.IsAuthenticated ? Color.Green : Color.Red;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_isAuthenticating)
            {
                e.Cancel = true;
                MessageBox.Show("Please wait for authentication to complete.", "Authentication in Progress", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // Unsubscribe from events
            _authenticationService.AuthenticationStateChanged -= AuthenticationService_AuthenticationStateChanged;

            base.OnFormClosing(e);
        }

        private void linkLabelSupport_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start("https://support.example.com");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Unable to open support link:\n\n{ex.Message}", 
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
