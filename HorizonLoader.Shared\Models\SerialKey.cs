using System;
using Newtonsoft.Json;

namespace HorizonLoader.Shared.Models
{
    /// <summary>
    /// Represents a serial key with product information and licensing details
    /// </summary>
    public class SerialKey
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("key")]
        public string Key { get; set; }

        [JsonProperty("productId")]
        public string ProductId { get; set; }

        [JsonProperty("productName")]
        public string ProductName { get; set; }

        [JsonProperty("createdDate")]
        public DateTime CreatedDate { get; set; }

        [JsonProperty("expirationDate")]
        public DateTime ExpirationDate { get; set; }

        [JsonProperty("isActive")]
        public bool IsActive { get; set; }

        [JsonProperty("boundHwid")]
        public string BoundHwid { get; set; }

        [JsonProperty("canResetHwid")]
        public bool CanResetHwid { get; set; }

        [JsonProperty("hwidResetCount")]
        public int HwidResetCount { get; set; }

        [JsonProperty("maxHwidResets")]
        public int MaxHwidResets { get; set; }

        [JsonProperty("lastUsedDate")]
        public DateTime? LastUsedDate { get; set; }

        [JsonProperty("createdBy")]
        public string CreatedBy { get; set; }

        public SerialKey()
        {
            Id = Guid.NewGuid().ToString();
            CreatedDate = DateTime.UtcNow;
            IsActive = true;
            CanResetHwid = true;
            HwidResetCount = 0;
            MaxHwidResets = 3;
        }

        /// <summary>
        /// Checks if the serial key is currently valid
        /// </summary>
        public bool IsValid()
        {
            return IsActive && DateTime.UtcNow <= ExpirationDate;
        }

        /// <summary>
        /// Checks if the serial key is expired
        /// </summary>
        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpirationDate;
        }

        /// <summary>
        /// Checks if HWID can be reset
        /// </summary>
        public bool CanPerformHwidReset()
        {
            return CanResetHwid && HwidResetCount < MaxHwidResets;
        }

        /// <summary>
        /// Performs HWID reset if allowed
        /// </summary>
        public bool ResetHwid()
        {
            if (!CanPerformHwidReset())
                return false;

            BoundHwid = null;
            HwidResetCount++;
            return true;
        }

        /// <summary>
        /// Binds the serial key to a specific hardware ID
        /// </summary>
        public void BindToHwid(string hwid)
        {
            BoundHwid = hwid;
            LastUsedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Checks if the serial key is bound to a specific HWID
        /// </summary>
        public bool IsBoundToHwid(string hwid)
        {
            return !string.IsNullOrEmpty(BoundHwid) && BoundHwid.Equals(hwid, StringComparison.OrdinalIgnoreCase);
        }
    }
}
