using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading;

namespace HorizonLoader.Shared.Security
{
    /// <summary>
    /// Provides anti-debugging and anti-tampering protection mechanisms
    /// </summary>
    public static class AntiDebug
    {
        #region Windows API Imports

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, [MarshalAs(UnmanagedType.Bool)] ref bool isDebuggerPresent);

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsDebuggerPresent();

        [DllImport("ntdll.dll", SetLastError = true, ExactSpelling = true)]
        private static extern int NtQueryInformationProcess(IntPtr processHandle, int processInformationClass, ref ProcessBasicInformation processInformation, int processInformationLength, ref int returnLength);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        [DllImport("kernel32.dll")]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll")]
        private static extern uint GetTickCount();

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool QueryPerformanceCounter(out long lpPerformanceCount);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool QueryPerformanceFrequency(out long lpFrequency);

        #endregion

        #region Structures

        [StructLayout(LayoutKind.Sequential)]
        private struct ProcessBasicInformation
        {
            public IntPtr Reserved1;
            public IntPtr PebBaseAddress;
            public IntPtr Reserved2_0;
            public IntPtr Reserved2_1;
            public IntPtr UniqueProcessId;
            public IntPtr InheritedFromUniqueProcessId;
        }

        #endregion

        private static bool _isProtectionEnabled = true;
        private static Timer _protectionTimer;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// Initializes anti-debugging protection
        /// </summary>
        public static void Initialize()
        {
            if (!_isProtectionEnabled)
                return;

            try
            {
                // Start continuous monitoring
                _protectionTimer = new Timer(PerformProtectionChecks, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
                
                // Perform initial checks
                PerformProtectionChecks(null);
            }
            catch (Exception)
            {
                // Silently handle initialization errors
            }
        }

        /// <summary>
        /// Disables protection (for debugging purposes only)
        /// </summary>
        public static void DisableProtection()
        {
            _isProtectionEnabled = false;
            _protectionTimer?.Dispose();
        }

        /// <summary>
        /// Performs various anti-debugging checks
        /// </summary>
        private static void PerformProtectionChecks(object state)
        {
            if (!_isProtectionEnabled)
                return;

            lock (_lockObject)
            {
                try
                {
                    // Check for debugger presence
                    if (IsDebuggerAttached())
                    {
                        HandleTamperingDetected("Debugger detected");
                        return;
                    }

                    // Check for remote debugger
                    if (IsRemoteDebuggerPresent())
                    {
                        HandleTamperingDetected("Remote debugger detected");
                        return;
                    }

                    // Check for timing attacks
                    if (IsTimingAttackDetected())
                    {
                        HandleTamperingDetected("Timing attack detected");
                        return;
                    }

                    // Check for known debugging tools
                    if (AreDebuggingToolsRunning())
                    {
                        HandleTamperingDetected("Debugging tools detected");
                        return;
                    }
                }
                catch (Exception)
                {
                    // Silently handle protection check errors
                }
            }
        }

        /// <summary>
        /// Checks if a debugger is attached using multiple methods
        /// </summary>
        public static bool IsDebuggerAttached()
        {
            try
            {
                // Method 1: IsDebuggerPresent API
                if (IsDebuggerPresent())
                    return true;

                // Method 2: Managed debugger check
                if (Debugger.IsAttached)
                    return true;

                // Method 3: PEB check
                if (IsPebDebuggerPresent())
                    return true;

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Checks for remote debugger presence
        /// </summary>
        private static bool IsRemoteDebuggerPresent()
        {
            try
            {
                bool isDebuggerPresent = false;
                CheckRemoteDebuggerPresent(GetCurrentProcess(), ref isDebuggerPresent);
                return isDebuggerPresent;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Checks PEB (Process Environment Block) for debugger flags
        /// </summary>
        private static bool IsPebDebuggerPresent()
        {
            try
            {
                ProcessBasicInformation pbi = new ProcessBasicInformation();
                int returnLength = 0;
                
                int status = NtQueryInformationProcess(GetCurrentProcess(), 0, ref pbi, Marshal.SizeOf(pbi), ref returnLength);
                
                if (status == 0 && pbi.PebBaseAddress != IntPtr.Zero)
                {
                    // Check BeingDebugged flag in PEB
                    byte beingDebugged = Marshal.ReadByte(pbi.PebBaseAddress, 2);
                    return beingDebugged != 0;
                }
                
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Detects timing attacks by measuring execution time
        /// </summary>
        private static bool IsTimingAttackDetected()
        {
            try
            {
                long start, end, frequency;
                QueryPerformanceFrequency(out frequency);
                QueryPerformanceCounter(out start);

                // Perform a simple operation
                int dummy = 0;
                for (int i = 0; i < 1000; i++)
                {
                    dummy += i;
                }

                QueryPerformanceCounter(out end);
                
                // Calculate execution time in milliseconds
                double executionTime = ((double)(end - start) / frequency) * 1000;
                
                // If execution took too long, it might indicate debugging
                return executionTime > 50; // 50ms threshold
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Checks for known debugging and reverse engineering tools
        /// </summary>
        private static bool AreDebuggingToolsRunning()
        {
            try
            {
                string[] debuggingTools = {
                    "ollydbg", "x64dbg", "x32dbg", "windbg", "ida", "ida64",
                    "cheatengine", "processhacker", "procmon", "procexp",
                    "wireshark", "fiddler", "dnspy", "reflexil", "de4dot",
                    "megadumper", "scylla", "importrec", "lordpe"
                };

                Process[] processes = Process.GetProcesses();
                
                foreach (Process process in processes)
                {
                    try
                    {
                        string processName = process.ProcessName.ToLower();
                        
                        foreach (string tool in debuggingTools)
                        {
                            if (processName.Contains(tool))
                            {
                                return true;
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // Process might have exited or access denied
                        continue;
                    }
                }
                
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Handles tampering detection
        /// </summary>
        private static void HandleTamperingDetected(string reason)
        {
            try
            {
                // Log the tampering attempt
                Debug.WriteLine($"Tampering detected: {reason}");
                
                // Terminate the application
                Environment.FailFast($"Security violation detected: {reason}");
            }
            catch (Exception)
            {
                // Force exit if FailFast doesn't work
                Environment.Exit(-1);
            }
        }

        /// <summary>
        /// Validates the integrity of the current assembly
        /// </summary>
        public static bool ValidateAssemblyIntegrity()
        {
            try
            {
                // Get the current assembly
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var location = assembly.Location;
                
                if (string.IsNullOrEmpty(location))
                    return false;

                // Check if file exists
                if (!System.IO.File.Exists(location))
                    return false;

                // Basic integrity check - verify file size hasn't changed dramatically
                var fileInfo = new System.IO.FileInfo(location);
                
                // If file is suspiciously small, it might be tampered
                if (fileInfo.Length < 1024) // Less than 1KB is suspicious for a .NET assembly
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Cleanup resources
        /// </summary>
        public static void Cleanup()
        {
            _protectionTimer?.Dispose();
            _protectionTimer = null;
        }
    }
}
